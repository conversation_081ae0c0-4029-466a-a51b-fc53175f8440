# 🚀 TaskDrive - Your Personal Task Management Solution

A modern, feature-rich MERN stack task management application with stunning UI/UX, real-time functionality, and comprehensive user management.

![TaskDrive Banner](https://via.placeholder.com/800x400/667eea/ffffff?text=TaskDrive+-+Organize+Your+Life)

## ✨ Features

### 🎨 **Beautiful Design**
- **Glassmorphism UI** with modern aesthetics
- **Animated backgrounds** and smooth transitions
- **Responsive design** that works on all devices
- **Dark/Light theme** toggle
- **Custom animations** and micro-interactions

### 🔐 **Secure Authentication**
- **JWT-based authentication** with secure cookies
- **Password hashing** with bcrypt
- **Session management** with express-session
- **Form validation** with real-time feedback
- **Flash messages** for user feedback

### 📋 **Advanced Task Management**
- **CRUD operations** (Create, Read, Update, Delete)
- **Task prioritization** (Low, Medium, High)
- **Status tracking** (Pending, In Progress, Completed)
- **Due date management**
- **Search and filter** functionality
- **Real-time task updates**

### 🛡️ **Enhanced Security**
- **Input validation** and sanitization
- **XSS protection**
- **CSRF protection**
- **Secure headers**
- **Rate limiting** (ready to implement)

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication tokens
- **bcrypt** - Password hashing
- **express-validator** - Input validation

### Frontend
- **EJS** - Template engine
- **TailwindCSS** - Utility-first CSS framework
- **Flowbite** - Component library
- **Font Awesome** - Icons
- **Custom CSS** - Enhanced styling

### Development Tools
- **Nodemon** - Development server
- **dotenv** - Environment variables
- **ESLint** - Code linting (ready to add)

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone the repository**
```bash
git clone <your-repo-url>
cd taskdrive
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
MONGO_URI=mongodb://localhost:27017/taskdrive
JWT_SECRET=your-super-secret-jwt-key-here
SESSION_SECRET=your-super-secret-session-key-here
PORT=3000
NODE_ENV=development
```

4. **Start the application**

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

5. **Build CSS (if needed)**
```bash
npm run build-css
```

## 📱 Usage

### Getting Started
1. Visit `http://localhost:3000`
2. Create a new account or login
3. Start managing your tasks!

### Key Features
- **Dashboard**: View all your tasks with statistics
- **Add Tasks**: Create new tasks with priority and due dates
- **Manage Tasks**: Update status, edit, or delete tasks
- **Search & Filter**: Find tasks quickly
- **Profile**: Manage your account settings

## 🎯 API Endpoints

### Authentication
- `GET /user/register` - Registration page
- `POST /user/register` - Create new user
- `GET /user/login` - Login page
- `POST /user/login` - Authenticate user
- `GET /user/logout` - Logout user

### Tasks
- `GET /home` - Dashboard with tasks
- `POST /tasks` - Create new task
- `PUT /tasks/:id` - Update task
- `PATCH /tasks/:id/toggle` - Toggle task status
- `DELETE /tasks/:id` - Delete task
- `GET /tasks/:id` - Get single task

## 🎨 Customization

### Themes
The app supports custom themes. Modify `/public/css/custom.css` to customize:
- Color schemes
- Animations
- Layout styles
- Component designs

### Components
- **Flash Messages**: Automatic notifications
- **Form Validation**: Real-time feedback
- **Loading States**: Enhanced UX
- **Error Handling**: Graceful error pages

## 🔧 Configuration

### Environment Variables
| Variable | Description | Default |
|----------|-------------|---------|
| `MONGO_URI` | MongoDB connection string | `mongodb://localhost:27017/taskdrive` |
| `JWT_SECRET` | JWT signing secret | Required |
| `SESSION_SECRET` | Session signing secret | Required |
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment mode | `development` |

### Database Schema

#### User Model
```javascript
{
  FirstName: String,
  LastName: String,
  email: String (unique),
  password: String (hashed),
  profilePicture: String,
  isActive: Boolean,
  lastLogin: Date,
  timestamps: true
}
```

#### Task Model
```javascript
{
  taskName: String,
  taskDescription: String,
  user: ObjectId (ref: User),
  status: String (pending|in-progress|completed),
  priority: String (low|medium|high),
  dueDate: Date,
  timestamps: true
}
```

## 🚀 Deployment

### Heroku
1. Create Heroku app
2. Set environment variables
3. Deploy with Git

### Vercel
1. Connect GitHub repository
2. Configure environment variables
3. Deploy automatically

### Docker (Coming Soon)
```dockerfile
# Dockerfile will be added in future updates
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **TailwindCSS** for the amazing utility classes
- **Flowbite** for beautiful components
- **Font Awesome** for the icon library
- **MongoDB** for the robust database
- **Express.js** for the powerful web framework

## 📞 Support

If you have any questions or need help, please:
- Open an issue on GitHub
- Contact the development team
- Check the documentation

---

**Made with ❤️ for productivity enthusiasts**
