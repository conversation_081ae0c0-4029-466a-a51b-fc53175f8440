const express = require('express');
const UserRouter = require(`./routes/user.routes`);
const dotenv = require(`dotenv`);
dotenv.config();
const connectToDB = require(`./config/db`);
connectToDB();
const cookieParser = require(`cookie-parser`);
const session = require(`express-session`);
const flash = require(`connect-flash`);
const IndexRouter = require(`./routes/index.routes`);
const path = require('path');

const app = express();

// View engine setup
app.set(`view engine`, `ejs`);
app.set('views', path.join(__dirname, 'views'));

// Static files
app.use(express.static(path.join(__dirname, 'public')));
app.use('/src', express.static(path.join(__dirname, 'src')));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true}));
app.use(cookieParser());

// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key-here',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true in production with HTTPS
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Flash messages
app.use(flash());

// Global variables for flash messages
app.use((req, res, next) => {
    res.locals.success_msg = req.flash('success_msg');
    res.locals.error_msg = req.flash('error_msg');
    res.locals.error = req.flash('error');
    next();
});

// Routes
app.use('/user', UserRouter);
app.use("/", IndexRouter);

// 404 handler
app.use((req, res) => {
    res.status(404).render('404', { title: 'Page Not Found' });
});

// Error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).render('error', {
        title: 'Server Error',
        error: process.env.NODE_ENV === 'development' ? err : {}
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, ()=>{
    console.log(`🚀 Server is running on port ${PORT}`);
    console.log(`📱 Visit: http://localhost:${PORT}`);
});