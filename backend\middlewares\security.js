const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const compression = require('compression');

// Rate limiting configurations
const createAccountLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 account creation requests per windowMs
  message: {
    success: false,
    message: 'Too many accounts created from this IP, please try again after an hour.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 login requests per windowMs
  message: {
    success: false,
    message: 'Too many login attempts from this IP, please try again after 15 minutes.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const taskLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // Limit each IP to 30 task operations per minute
  message: {
    success: false,
    message: 'Too many task operations, please slow down.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Security headers configuration
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false,
});

// XSS Protection middleware
const xssProtection = (req, res, next) => {
  // Clean user input from malicious HTML
  if (req.body) {
    req.body = xss(req.body);
  }
  if (req.query) {
    req.query = xss(req.query);
  }
  if (req.params) {
    req.params = xss(req.params);
  }
  next();
};

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Remove any keys that start with '$' or contain '.'
  mongoSanitize()(req, res, next);
};

// Security logging middleware
const securityLogger = (req, res, next) => {
  const timestamp = new Date().toISOString();
  const ip = req.ip || req.connection.remoteAddress;
  const userAgent = req.get('User-Agent') || 'Unknown';
  
  // Log suspicious activities
  if (req.body && typeof req.body === 'object') {
    const bodyStr = JSON.stringify(req.body);
    if (bodyStr.includes('<script>') || bodyStr.includes('javascript:') || bodyStr.includes('onload=')) {
      console.warn(`🚨 [SECURITY] Potential XSS attempt from ${ip} at ${timestamp}`);
      console.warn(`🚨 [SECURITY] User-Agent: ${userAgent}`);
      console.warn(`🚨 [SECURITY] Request body: ${bodyStr}`);
    }
  }
  
  next();
};

// Error handling for security middleware
const securityErrorHandler = (err, req, res, next) => {
  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      success: false,
      message: 'Request entity too large'
    });
  }
  
  if (err.type === 'entity.parse.failed') {
    return res.status(400).json({
      success: false,
      message: 'Invalid JSON format'
    });
  }
  
  next(err);
};

module.exports = {
  createAccountLimiter,
  loginLimiter,
  generalLimiter,
  taskLimiter,
  helmetConfig,
  xssProtection,
  sanitizeInput,
  securityLogger,
  securityErrorHandler,
  compression: compression()
};
