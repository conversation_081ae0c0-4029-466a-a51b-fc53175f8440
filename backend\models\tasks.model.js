const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
   taskName: {
     type: String,
     required: [true, 'Task name is required'],
     trim: true,
     minlength: [3, 'Task name must be at least 3 characters long'],
     maxlength: [100, 'Task name cannot exceed 100 characters']
   },
   taskDescription: {
     type: String,
     required: [true, 'Task description is required'],
     trim: true,
     minlength: [5, 'Task description must be at least 5 characters long'],
     maxlength: [500, 'Task description cannot exceed 500 characters']
   },
   user: {
     type: mongoose.Schema.Types.ObjectId,
     ref: 'user',
     required: [true, 'User reference is required']
   },
   status: {
     type: String,
     enum: ['pending', 'in-progress', 'completed'],
     default: 'pending'
   },
   priority: {
     type: String,
     enum: ['low', 'medium', 'high'],
     default: 'medium'
   },
   dueDate: {
     type: Date,
     default: null
   },
   createdAt: {
     type: Date,
     default: Date.now
   },
   updatedAt: {
     type: Date,
     default: Date.now
   }
}, {
   timestamps: true
});

// Update the updatedAt field before saving
taskSchema.pre('save', function(next) {
   this.updatedAt = Date.now();
   next();
});

const task = mongoose.model(`task`, taskSchema);

module.exports = task;