{"name": "taskdrive-backend", "version": "1.0.0", "description": "TaskDrive MERN Stack Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mern", "task-management", "api", "express", "mongodb"], "author": "TaskDrive Team", "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.2.0", "hpp": "^0.2.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}