{"name": "taskdrive-backend", "version": "1.0.0", "description": "TaskDrive MERN Stack Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mern", "task-management", "api", "express", "mongodb"], "author": "TaskDrive Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}