const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');
const auth = require('../middlewares/auth');

// Import validation and security middleware
const {
  validate,
  schemas,
  expressValidatorRules,
  handleValidationErrors
} = require('../middlewares/validation');

const {
  catchAsync,
  AuthenticationError,
  ConflictError,
  logger,
  logSecurityEvent
} = require('../middlewares/errorHandler');

const router = express.Router();

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register',
  validate(schemas.register),
  ...expressValidatorRules.register,
  handleValidationErrors,
  catchAsync(async (req, res) => {
    const { FirstName, LastName, email, password } = req.body;

    // Log registration attempt
    logger.info(`Registration attempt for email: ${email}`, req);

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      logSecurityEvent('DUPLICATE_REGISTRATION_ATTEMPT', req, { email });
      throw new ConflictError('User with this email already exists');
    }

    // Hash password with high salt rounds for security
    const saltRounds = 14;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create new user
    const newUser = new User({
      FirstName: FirstName.trim(),
      LastName: LastName.trim(),
      email: email.toLowerCase(),
      password: hashedPassword,
      registrationIP: req.ip,
      lastLoginIP: req.ip
    });

    await newUser.save();

    // Generate JWT token with shorter expiry for security
    const token = jwt.sign(
      {
        userId: newUser._id,
        email: newUser.email,
        iat: Math.floor(Date.now() / 1000)
      },
      process.env.JWT_SECRET,
      {
        expiresIn: '7d',
        issuer: 'taskdrive-api',
        audience: 'taskdrive-client'
      }
    );

    // Log successful registration
    logger.info(`User registered successfully: ${email}`, req);
    logSecurityEvent('USER_REGISTERED', req, {
      userId: newUser._id,
      email: newUser.email
    });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: newUser._id,
        FirstName: newUser.FirstName,
        LastName: newUser.LastName,
        email: newUser.email,
        fullName: newUser.fullName
      }
    });
  })
);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login',
  validate(schemas.login),
  handleValidationErrors,
  catchAsync(async (req, res) => {
    const { email, password } = req.body;

    // Log login attempt
    logger.info(`Login attempt for email: ${email}`, req);

    // Find user by email with case-insensitive search
    const user = await User.findOne({ email: email.toLowerCase() }).select('+password');
    if (!user) {
      logSecurityEvent('FAILED_LOGIN_ATTEMPT', req, {
        email,
        reason: 'user_not_found'
      });
      throw new AuthenticationError('Invalid email or password');
    }

    // Check if account is locked (future enhancement)
    if (user.accountLocked && user.lockUntil > Date.now()) {
      logSecurityEvent('LOGIN_ATTEMPT_LOCKED_ACCOUNT', req, {
        userId: user._id,
        email
      });
      throw new AuthenticationError('Account temporarily locked due to too many failed attempts');
    }

    // Check password with timing attack protection
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      // Increment failed login attempts
      user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
      user.lastFailedLogin = new Date();

      // Lock account after 5 failed attempts
      if (user.failedLoginAttempts >= 5) {
        user.accountLocked = true;
        user.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      }

      await user.save();

      logSecurityEvent('FAILED_LOGIN_ATTEMPT', req, {
        userId: user._id,
        email,
        reason: 'invalid_password',
        failedAttempts: user.failedLoginAttempts
      });

      throw new AuthenticationError('Invalid email or password');
    }

    // Reset failed login attempts on successful login
    user.failedLoginAttempts = 0;
    user.accountLocked = false;
    user.lockUntil = undefined;
    user.lastLogin = new Date();
    user.lastLoginIP = req.ip;
    await user.save();

    // Generate JWT token with enhanced security
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        iat: Math.floor(Date.now() / 1000)
      },
      process.env.JWT_SECRET,
      {
        expiresIn: '7d',
        issuer: 'taskdrive-api',
        audience: 'taskdrive-client'
      }
    );

    // Log successful login
    logger.info(`User logged in successfully: ${email}`, req);
    logSecurityEvent('USER_LOGIN', req, {
      userId: user._id,
      email: user.email
    });

    res.status(200).json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        FirstName: user.FirstName,
        LastName: user.LastName,
        email: user.email,
        fullName: user.fullName,
        lastLogin: user.lastLogin
      }
    });
  })
);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    
    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        FirstName: user.FirstName,
        LastName: user.LastName,
        email: user.email,
        fullName: user.fullName,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user (client-side token removal)
// @access  Private
router.post('/logout', auth, (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Logout successful'
  });
});

module.exports = router;
