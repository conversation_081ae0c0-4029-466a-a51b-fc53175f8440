const express = require('express');
const Task = require('../models/tasks.model');
const auth = require('../middlewares/auth');

// Import validation and security middleware
const {
  validate,
  schemas,
  validateObjectId
} = require('../middlewares/validation');

const {
  catchAsync,
  NotFoundError,
  AuthorizationError,
  logger,
  logSecurityEvent
} = require('../middlewares/errorHandler');

const router = express.Router();

// @route   GET /api/tasks
// @desc    Get all tasks for the authenticated user
// @access  Private
router.get('/',
  auth,
  catchAsync(async (req, res) => {
    const { status, priority, search, sortBy = 'createdAt', order = 'desc', limit = 100 } = req.query;

    // Validate and sanitize query parameters
    const validSortFields = ['createdAt', 'updatedAt', 'taskName', 'priority', 'dueDate'];
    const validSortBy = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const validOrder = order === 'asc' ? 'asc' : 'desc';
    const validLimit = Math.min(parseInt(limit) || 100, 1000); // Max 1000 tasks

    // Build query with user isolation
    let query = { user: req.user.id };

    if (status && status !== 'all') {
      const validStatuses = ['pending', 'in-progress', 'completed'];
      if (validStatuses.includes(status)) {
        query.status = status;
      }
    }

    if (priority && priority !== 'all') {
      const validPriorities = ['low', 'medium', 'high'];
      if (validPriorities.includes(priority)) {
        query.priority = priority;
      }
    }

    if (search) {
      // Sanitize search input to prevent injection
      const sanitizedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      query.$or = [
        { taskName: { $regex: sanitizedSearch, $options: 'i' } },
        { taskDescription: { $regex: sanitizedSearch, $options: 'i' } }
      ];
    }

    // Build sort object
    const sortOrder = validOrder === 'desc' ? -1 : 1;
    const sortObj = { [validSortBy]: sortOrder };

    const tasks = await Task.find(query)
      .sort(sortObj)
      .limit(validLimit)
      .lean(); // Use lean for better performance

    // Calculate statistics
    const stats = {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'completed').length,
      pending: tasks.filter(t => t.status === 'pending').length,
      inProgress: tasks.filter(t => t.status === 'in-progress').length,
      overdue: tasks.filter(t => {
        if (!t.dueDate) return false;
        return new Date(t.dueDate) < new Date() && t.status !== 'completed';
      }).length
    };

    res.status(200).json({
      success: true,
      count: tasks.length,
      stats,
      tasks
    });
  })
);

// @route   GET /api/tasks/:id
// @desc    Get single task
// @access  Private
router.get('/:id',
  auth,
  validateObjectId,
  catchAsync(async (req, res) => {
    const task = await Task.findOne({
      _id: req.params.id,
      user: req.user.id
    }).lean();

    if (!task) {
      throw new NotFoundError('Task not found');
    }

    res.status(200).json({
      success: true,
      task
    });
  })
);

// @route   POST /api/tasks
// @desc    Create a new task
// @access  Private
router.post('/',
  auth,
  validate(schemas.task),
  catchAsync(async (req, res) => {
    const { taskName, taskDescription, priority, dueDate } = req.body;

    // Log task creation
    logger.info(`Task creation attempt by user: ${req.user.id}`, req);

    // Check user's task limit (prevent spam)
    const userTaskCount = await Task.countDocuments({ user: req.user.id });
    if (userTaskCount >= 1000) { // Reasonable limit
      logSecurityEvent('TASK_LIMIT_EXCEEDED', req, {
        userId: req.user.id,
        currentCount: userTaskCount
      });
      return res.status(429).json({
        success: false,
        message: 'Task limit exceeded. Please delete some tasks before creating new ones.'
      });
    }

    const newTask = new Task({
      taskName: taskName.trim(),
      taskDescription: taskDescription.trim(),
      priority: priority || 'medium',
      dueDate: dueDate || null,
      user: req.user.id,
      createdIP: req.ip
    });

    await newTask.save();

    logger.info(`Task created successfully: ${newTask._id}`, req);

    res.status(201).json({
      success: true,
      message: 'Task created successfully',
      task: newTask
    });
  })
);

// @route   PUT /api/tasks/:id
// @desc    Update a task
// @access  Private
router.put('/:id',
  auth,
  validateObjectId,
  validate(schemas.updateTask),
  catchAsync(async (req, res) => {
    const updateData = req.body;
    updateData.updatedAt = new Date();

    const task = await Task.findOneAndUpdate(
      { _id: req.params.id, user: req.user.id },
      updateData,
      { new: true, runValidators: true }
    );

    if (!task) {
      throw new NotFoundError('Task not found');
    }

    logger.info(`Task updated: ${task._id}`, req);

    res.status(200).json({
      success: true,
      message: 'Task updated successfully',
      task
    });
  })
);

// @route   PATCH /api/tasks/:id/toggle
// @desc    Toggle task status
// @access  Private
router.patch('/:id/toggle',
  auth,
  validateObjectId,
  catchAsync(async (req, res) => {
    const task = await Task.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!task) {
      throw new NotFoundError('Task not found');
    }

    // Toggle status logic
    let newStatus;
    switch (task.status) {
      case 'pending':
        newStatus = 'in-progress';
        break;
      case 'in-progress':
        newStatus = 'completed';
        break;
      case 'completed':
        newStatus = 'pending';
        break;
      default:
        newStatus = 'pending';
    }

    task.status = newStatus;
    task.updatedAt = new Date();
    await task.save();

    logger.info(`Task status toggled: ${task._id} -> ${newStatus}`, req);

    res.status(200).json({
      success: true,
      message: 'Task status updated successfully',
      task
    });
  })
);

// @route   DELETE /api/tasks/:id
// @desc    Delete a task
// @access  Private
router.delete('/:id',
  auth,
  validateObjectId,
  catchAsync(async (req, res) => {
    const task = await Task.findOneAndDelete({
      _id: req.params.id,
      user: req.user.id
    });

    if (!task) {
      throw new NotFoundError('Task not found');
    }

    logger.info(`Task deleted: ${task._id}`, req);
    logSecurityEvent('TASK_DELETED', req, {
      taskId: task._id,
      taskName: task.taskName
    });

    res.status(200).json({
      success: true,
      message: 'Task deleted successfully'
    });
  })
);

module.exports = router;
