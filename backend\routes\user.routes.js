const express = require('express');
const auth = require('../middlewares/auth');

const router = express.Router();

// @route   GET /api/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', auth, async (req, res) => {
  try {
    const User = require('../models/user.model');
    const user = await User.findById(req.user.id).select('-password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        FirstName: user.FirstName,
        LastName: user.LastName,
        email: user.email,
        fullName: user.fullName,
        profilePicture: user.profilePicture,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
    
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching profile'
    });
  }
});

// @route   GET /api/users/stats
// @desc    Get user statistics
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    const Task = require('../models/tasks.model');
    
    // Get task statistics
    const totalTasks = await Task.countDocuments({ user: req.user.id });
    const completedTasks = await Task.countDocuments({ 
      user: req.user.id, 
      status: 'completed' 
    });
    const pendingTasks = await Task.countDocuments({ 
      user: req.user.id, 
      status: 'pending' 
    });
    const inProgressTasks = await Task.countDocuments({ 
      user: req.user.id, 
      status: 'in-progress' 
    });
    
    // Get overdue tasks
    const overdueTasks = await Task.countDocuments({
      user: req.user.id,
      dueDate: { $lt: new Date() },
      status: { $ne: 'completed' }
    });
    
    // Calculate completion rate
    const completionRate = totalTasks > 0 ? 
      Math.round((completedTasks / totalTasks) * 100) : 0;
    
    res.status(200).json({
      success: true,
      stats: {
        totalTasks,
        completedTasks,
        pendingTasks,
        inProgressTasks,
        overdueTasks,
        completionRate
      }
    });
    
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching statistics'
    });
  }
});

module.exports = router;
