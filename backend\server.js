const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const connectToDB = require('./config/db');

// Load environment variables
dotenv.config({ path: '../.env' });

// Fallback environment variables if .env is not loaded
if (!process.env.MONGO_URI) {
  process.env.MONGO_URI = 'mongodb://localhost:27017/taskdrive';
}
if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'abdullah-drive';
}

// Import security middleware
const {
  createAccountLimiter,
  loginLimiter,
  generalLimiter,
  taskLimiter,
  helmetConfig,
  xssProtection,
  sanitizeInput,
  securityLogger,
  securityErrorHandler,
  compression
} = require('./middlewares/security');

const {
  globalErrorHandler,
  handleNotFound,
  logger
} = require('./middlewares/errorHandler');

// Connect to MongoDB
connectToDB();

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware - Apply early
app.use(helmetConfig);
app.use(compression);
app.use(securityLogger);

// Rate limiting - Apply to all routes
app.use(generalLimiter);

// Body parsing middleware with enhanced security
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization and XSS protection
app.use(sanitizeInput);
app.use(xssProtection);

// CORS configuration for React frontend
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', process.env.FRONTEND_URL].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']
}));

// Security error handling
app.use(securityErrorHandler);

// Root API endpoint
app.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'TaskDrive API is running',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      auth: '/api/auth',
      tasks: '/api/tasks',
      users: '/api/users'
    },
    timestamp: new Date().toISOString()
  });
});

// API Routes with specific rate limiting
app.use('/api/auth/register', createAccountLimiter);
app.use('/api/auth/login', loginLimiter);
app.use('/api/tasks', taskLimiter);

app.use('/api/auth', require('./routes/auth.routes'));
app.use('/api/tasks', require('./routes/task.routes'));
app.use('/api/users', require('./routes/user.routes'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'TaskDrive API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Handle unhandled routes
app.use('/api/*', handleNotFound);

// Global error handler
app.use(globalErrorHandler);

const PORT = process.env.BACKEND_PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 TaskDrive Backend API running on port ${PORT}`);
  console.log(`📱 API URL: http://localhost:${PORT}/api`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
