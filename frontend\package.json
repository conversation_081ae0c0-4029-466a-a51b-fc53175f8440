{"name": "taskdrive-frontend", "version": "1.0.0", "description": "TaskDrive MERN Stack Frontend - React with Framer Motion", "private": true, "dependencies": {"@types/node": "^24.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.8.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.0", "vite": "^5.0.0"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}