<!DOCTYPE html>
<html>
<head>
    <title>TaskDrive Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
        .icon-item { text-align: center; }
    </style>
</head>
<body>
    <h1>TaskDrive PWA Icon Generator</h1>
    <p>This page generates the required PWA icons for TaskDrive. Right-click and save each icon.</p>
    
    <div class="icon-grid" id="iconGrid"></div>

    <script>
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        const iconGrid = document.getElementById('iconGrid');

        sizes.forEach(size => {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            const radius = size * 0.2;
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';

            // Add rocket emoji or text
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (size >= 128) {
                // Large icons - use emoji
                ctx.font = `${size * 0.5}px Arial`;
                ctx.fillText('🚀', size / 2, size / 2);
            } else {
                // Small icons - use text
                ctx.font = `bold ${size * 0.25}px Arial`;
                ctx.fillText('TD', size / 2, size / 2);
            }

            // Create container
            const container = document.createElement('div');
            container.className = 'icon-item';
            
            const label = document.createElement('p');
            label.textContent = `${size}x${size}`;
            
            const link = document.createElement('a');
            link.href = canvas.toDataURL();
            link.download = `icon-${size}x${size}.png`;
            link.appendChild(canvas);
            
            container.appendChild(label);
            container.appendChild(link);
            iconGrid.appendChild(container);
        });

        // Add CanvasRenderingContext2D.roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
