// TaskDrive Service Worker - Advanced PWA Features
const CACHE_NAME = 'taskdrive-v1.0.0';
const STATIC_CACHE = 'taskdrive-static-v1.0.0';
const DYNAMIC_CACHE = 'taskdrive-dynamic-v1.0.0';
const API_CACHE = 'taskdrive-api-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  // Add other static assets as needed
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/auth/me',
  '/api/tasks',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('📦 Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('✅ Service Worker: Activating...');
  
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== API_CACHE) {
              console.log('🗑️ Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      self.clients.claim()
    ])
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static assets
  if (request.destination === 'image' || 
      request.destination === 'style' || 
      request.destination === 'script') {
    event.respondWith(handleStaticAssets(request));
    return;
  }

  // Handle navigation requests
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigation(request));
    return;
  }

  // Default: network first, then cache
  event.respondWith(
    fetch(request).catch(() => {
      return caches.match(request);
    })
  );
});

// API request handler - Cache first for GET, Network only for mutations
async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE);
  
  if (request.method === 'GET') {
    // Cache first strategy for GET requests
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // Return cached version and update in background
      fetchAndCache(request, cache);
      return cachedResponse;
    }
    
    // No cache, fetch from network
    try {
      const response = await fetch(request);
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
    } catch (error) {
      // Return offline fallback for tasks
      if (request.url.includes('/api/tasks')) {
        return new Response(JSON.stringify({
          success: false,
          message: 'You are offline. Please check your connection.',
          offline: true,
          tasks: []
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      throw error;
    }
  } else {
    // Network only for POST, PUT, DELETE, PATCH
    try {
      const response = await fetch(request);
      
      // If successful, invalidate related cache entries
      if (response.ok && request.url.includes('/api/tasks')) {
        const keys = await cache.keys();
        const tasksKeys = keys.filter(key => key.url.includes('/api/tasks'));
        await Promise.all(tasksKeys.map(key => cache.delete(key)));
      }
      
      return response;
    } catch (error) {
      // Store failed requests for retry when online
      await storeFailedRequest(request);
      
      return new Response(JSON.stringify({
        success: false,
        message: 'Request failed. Will retry when online.',
        offline: true
      }), {
        status: 202,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
}

// Static assets handler - Cache first
async function handleStaticAssets(request) {
  const cache = await caches.open(STATIC_CACHE);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const response = await fetch(request);
    if (response.ok) {
      cache.put(request, response.clone());
    }
    return response;
  } catch (error) {
    // Return fallback for images
    if (request.destination === 'image') {
      return new Response(
        '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#f3f4f6"/><text x="100" y="100" text-anchor="middle" dy=".3em" fill="#9ca3af">Image Offline</text></svg>',
        { headers: { 'Content-Type': 'image/svg+xml' } }
      );
    }
    throw error;
  }
}

// Navigation handler - App shell pattern
async function handleNavigation(request) {
  try {
    const response = await fetch(request);
    
    // Cache successful navigation responses
    if (response.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    // Return cached version or app shell
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return app shell (index.html)
    return cache.match('/');
  }
}

// Background fetch and cache
async function fetchAndCache(request, cache) {
  try {
    const response = await fetch(request);
    if (response.ok) {
      cache.put(request, response.clone());
    }
  } catch (error) {
    console.log('Background fetch failed:', error);
  }
}

// Store failed requests for retry
async function storeFailedRequest(request) {
  const failedRequests = await getFailedRequests();
  const requestData = {
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries()),
    body: request.method !== 'GET' ? await request.text() : null,
    timestamp: Date.now()
  };
  
  failedRequests.push(requestData);
  await setFailedRequests(failedRequests);
}

// Get failed requests from IndexedDB
async function getFailedRequests() {
  return new Promise((resolve) => {
    const request = indexedDB.open('TaskDriveOffline', 1);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('failedRequests')) {
        db.createObjectStore('failedRequests', { keyPath: 'id', autoIncrement: true });
      }
    };
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['failedRequests'], 'readonly');
      const store = transaction.objectStore('failedRequests');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => {
        resolve(getAllRequest.result || []);
      };
      
      getAllRequest.onerror = () => {
        resolve([]);
      };
    };
    
    request.onerror = () => {
      resolve([]);
    };
  });
}

// Set failed requests to IndexedDB
async function setFailedRequests(requests) {
  return new Promise((resolve) => {
    const request = indexedDB.open('TaskDriveOffline', 1);
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['failedRequests'], 'readwrite');
      const store = transaction.objectStore('failedRequests');
      
      // Clear existing requests
      store.clear();
      
      // Add new requests
      requests.forEach(req => store.add(req));
      
      transaction.oncomplete = () => resolve();
      transaction.onerror = () => resolve();
    };
    
    request.onerror = () => resolve();
  });
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'retry-failed-requests') {
    event.waitUntil(retryFailedRequests());
  }
});

// Retry failed requests when online
async function retryFailedRequests() {
  const failedRequests = await getFailedRequests();
  const successfulRequests = [];
  
  for (const requestData of failedRequests) {
    try {
      const response = await fetch(requestData.url, {
        method: requestData.method,
        headers: requestData.headers,
        body: requestData.body
      });
      
      if (response.ok) {
        successfulRequests.push(requestData);
      }
    } catch (error) {
      console.log('Retry failed for:', requestData.url);
    }
  }
  
  // Remove successful requests from failed queue
  const remainingRequests = failedRequests.filter(
    req => !successfulRequests.includes(req)
  );
  
  await setFailedRequests(remainingRequests);
}

// Push notification handler
self.addEventListener('push', (event) => {
  const options = {
    body: 'You have new task updates!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Tasks',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/xmark.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.data = { ...options.data, ...data };
  }
  
  event.waitUntil(
    self.registration.showNotification('TaskDrive', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('🚀 TaskDrive Service Worker loaded successfully!');
