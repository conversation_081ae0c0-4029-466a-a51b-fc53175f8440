import { useState, useEffect } from 'react';
import axios from 'axios';

interface User {
  FirstName: string;
  LastName: string;
  email: string;
  id?: string;
}

interface Task {
  _id: string;
  taskName: string;
  taskDescription: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  dueDate?: string;
  createdAt: string;
}

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskName, setTaskName] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [dueDate, setDueDate] = useState('');
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    if (token) {
      fetchUserAndTasks();
    } else {
      // Auto login for demo
      autoLogin();
    }
  }, [token]);

  const autoLogin = async () => {
    try {
      const response = await axios.post('http://localhost:5000/api/auth/login', {
        email: '<EMAIL>',
        password: 'TestPass123'
      });

      if (response.data.success) {
        setToken(response.data.token);
        setUser(response.data.user);
        localStorage.setItem('token', response.data.token);
      }
    } catch (error) {
      console.error('Auto login failed:', error);
      // Set a dummy user if login fails
      setUser({
        FirstName: 'Demo',
        LastName: 'User',
        email: '<EMAIL>'
      });
    }
  };

  const fetchUserAndTasks = async () => {
    try {
      const tasksResponse = await axios.get('http://localhost:5000/api/tasks', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (tasksResponse.data.success) {
        setTasks(tasksResponse.data.tasks);
      }

      const userResponse = await axios.get('http://localhost:5000/api/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (userResponse.data.success) {
        setUser(userResponse.data.user);
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
    }
  };

  const createTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!taskName.trim() || !taskDescription.trim()) return;

    try {
      const response = await axios.post('http://localhost:5000/api/tasks', {
        taskName,
        taskDescription,
        priority,
        dueDate: dueDate || undefined
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setTaskName('');
        setTaskDescription('');
        setPriority('medium');
        setDueDate('');
        fetchUserAndTasks();
      }
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  const toggleTaskStatus = async (taskId: string) => {
    try {
      await axios.patch(`http://localhost:5000/api/tasks/${taskId}/toggle`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchUserAndTasks();
    } catch (error) {
      console.error('Failed to toggle task:', error);
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
      await axios.delete(`http://localhost:5000/api/tasks/${taskId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchUserAndTasks();
    } catch (error) {
      console.error('Failed to delete task:', error);
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    setTasks([]);
    localStorage.removeItem('token');
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.taskName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.taskDescription.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || task.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const completedTasks = tasks.filter(task => task.status === 'completed').length;
  const pendingTasks = tasks.filter(task => task.status === 'pending').length;

  if (!user) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ color: 'white', textAlign: 'center' }}>
          <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>🚀 TaskDrive</h1>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Navigation */}
      <nav style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        position: 'fixed',
        width: '100%',
        zIndex: 40,
        top: 0
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '4rem' }}>
            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white', margin: 0 }}>
              <i className="fas fa-tasks" style={{ marginRight: '0.5rem' }}></i>TaskDrive
            </h1>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <span style={{ color: 'white' }}>Welcome, {user.FirstName}!</span>
              <button
                onClick={logout}
                style={{
                  color: 'white',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.5rem',
                  transition: 'background-color 0.3s'
                }}
                onMouseOver={(e) => (e.target as HTMLElement).style.backgroundColor = 'rgba(255,255,255,0.1)'}
                onMouseOut={(e) => (e.target as HTMLElement).style.backgroundColor = 'transparent'}
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div style={{ paddingTop: '5rem', paddingBottom: '2rem', padding: '5rem 1rem 2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {/* Dashboard Header */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{ fontSize: '2.5rem', fontWeight: 'bold', color: 'white', marginBottom: '1rem' }}>
              Your Task Dashboard
            </h2>
            <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '1.125rem' }}>
              Manage your tasks efficiently and stay productive
            </p>
          </div>

          {/* Quick Stats */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              transition: 'transform 0.3s'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  backgroundColor: '#3b82f6',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '1rem'
                }}>
                  <span style={{ color: 'white', fontSize: '1.25rem' }}>📋</span>
                </div>
                <div>
                  <h3 style={{ fontSize: '2rem', fontWeight: 'bold', color: 'white', margin: 0 }}>{tasks.length}</h3>
                  <p style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>Total Tasks</p>
                </div>
              </div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  backgroundColor: '#10b981',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '1rem'
                }}>
                  <span style={{ color: 'white', fontSize: '1.25rem' }}>✅</span>
                </div>
                <div>
                  <h3 style={{ fontSize: '2rem', fontWeight: 'bold', color: 'white', margin: 0 }}>{completedTasks}</h3>
                  <p style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>Completed</p>
                </div>
              </div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  backgroundColor: '#f59e0b',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '1rem'
                }}>
                  <span style={{ color: 'white', fontSize: '1.25rem' }}>⏰</span>
                </div>
                <div>
                  <h3 style={{ fontSize: '2rem', fontWeight: 'bold', color: 'white', margin: 0 }}>{pendingTasks}</h3>
                  <p style={{ color: 'rgba(255,255,255,0.8)', margin: 0 }}>Pending</p>
                </div>
              </div>
            </div>
          </div>

          {/* Task Management Section */}
          <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '2rem' }}>
            {/* Task List */}
            <div>
              <div style={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                borderRadius: '1rem',
                padding: '1.5rem'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem', flexWrap: 'wrap', gap: '1rem' }}>
                  <h3 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white', margin: 0 }}>Your Tasks</h3>
                  <div style={{ display: 'flex', gap: '0.75rem', flexWrap: 'wrap' }}>
                    <input
                      type="text"
                      placeholder="Search tasks..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: 'none',
                        borderRadius: '0.5rem',
                        padding: '0.5rem 1rem',
                        color: 'white',
                        fontSize: '0.875rem',
                        minWidth: '200px'
                      }}
                    />
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      style={{
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: 'none',
                        borderRadius: '0.5rem',
                        padding: '0.5rem 1rem',
                        color: 'white',
                        fontSize: '0.875rem'
                      }}
                    >
                      <option value="all">All Tasks</option>
                      <option value="pending">Pending</option>
                      <option value="in-progress">In Progress</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>
                </div>

                {/* Task List */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {filteredTasks.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '3rem' }}>
                      <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📋</div>
                      <h4 style={{ fontSize: '1.25rem', fontWeight: '600', color: 'rgba(255,255,255,0.7)', marginBottom: '0.5rem' }}>No tasks yet</h4>
                      <p style={{ color: 'rgba(255,255,255,0.5)' }}>Create your first task to get started!</p>
                    </div>
                  ) : (
                    filteredTasks.map((task) => (
                      <div
                        key={task._id}
                        style={{
                          background: 'rgba(255, 255, 255, 0.1)',
                          borderRadius: '0.75rem',
                          padding: '1rem',
                          borderLeft: `4px solid ${task.priority === 'high' ? '#ef4444' : task.priority === 'medium' ? '#f59e0b' : '#10b981'}`,
                          transition: 'transform 0.2s'
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <div style={{ flex: 1 }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem', flexWrap: 'wrap', gap: '0.5rem' }}>
                              <h4 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'white', margin: 0 }}>
                                {task.taskName}
                              </h4>
                              <span style={{
                                padding: '0.25rem 0.5rem',
                                borderRadius: '9999px',
                                fontSize: '0.75rem',
                                fontWeight: '600',
                                backgroundColor: task.status === 'completed' ? '#10b981' : task.status === 'in-progress' ? '#3b82f6' : '#f59e0b',
                                color: 'white'
                              }}>
                                {task.status.replace('-', ' ')}
                              </span>
                              <span style={{
                                padding: '0.25rem 0.5rem',
                                borderRadius: '9999px',
                                fontSize: '0.75rem',
                                backgroundColor: task.priority === 'high' ? '#fecaca' : task.priority === 'medium' ? '#fed7aa' : '#d1fae5',
                                color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#ea580c' : '#059669'
                              }}>
                                {task.priority}
                              </span>
                            </div>
                            <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: '0.75rem', margin: 0 }}>
                              {task.taskDescription}
                            </p>
                            <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: 'rgba(255,255,255,0.6)', gap: '1rem' }}>
                              <span>📅 Created: {new Date(task.createdAt).toLocaleDateString()}</span>
                              {task.dueDate && (
                                <span>⏰ Due: {new Date(task.dueDate).toLocaleDateString()}</span>
                              )}
                            </div>
                          </div>
                          <div style={{ display: 'flex', gap: '0.5rem', marginLeft: '1rem' }}>
                            <button
                              onClick={() => toggleTaskStatus(task._id)}
                              style={{
                                background: 'rgba(59, 130, 246, 0.2)',
                                border: 'none',
                                borderRadius: '0.5rem',
                                padding: '0.5rem',
                                color: '#60a5fa',
                                cursor: 'pointer',
                                transition: 'background-color 0.2s'
                              }}
                              title="Toggle Status"
                            >
                              🔄
                            </button>
                            <button
                              onClick={() => deleteTask(task._id)}
                              style={{
                                background: 'rgba(239, 68, 68, 0.2)',
                                border: 'none',
                                borderRadius: '0.5rem',
                                padding: '0.5rem',
                                color: '#f87171',
                                cursor: 'pointer',
                                transition: 'background-color 0.2s'
                              }}
                              title="Delete Task"
                            >
                              🗑️
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Add Task Panel */}
            <div>
              <div style={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                borderRadius: '1rem',
                padding: '1.5rem',
                position: 'sticky',
                top: '6rem'
              }}>
                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: 'white', marginBottom: '1.5rem' }}>
                  ➕ Add New Task
                </h3>

                <form onSubmit={createTask} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500', color: 'white' }}>
                      Task Name
                    </label>
                    <input
                      type="text"
                      value={taskName}
                      onChange={(e) => setTaskName(e.target.value)}
                      placeholder="Enter task name"
                      required
                      style={{
                        width: '100%',
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: 'none',
                        borderRadius: '0.5rem',
                        padding: '0.75rem',
                        color: 'white',
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500', color: 'white' }}>
                      Description
                    </label>
                    <textarea
                      value={taskDescription}
                      onChange={(e) => setTaskDescription(e.target.value)}
                      rows={3}
                      placeholder="Describe your task"
                      required
                      style={{
                        width: '100%',
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: 'none',
                        borderRadius: '0.5rem',
                        padding: '0.75rem',
                        color: 'white',
                        fontSize: '1rem',
                        resize: 'none'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500', color: 'white' }}>
                      Priority
                    </label>
                    <select
                      value={priority}
                      onChange={(e) => setPriority(e.target.value)}
                      style={{
                        width: '100%',
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: 'none',
                        borderRadius: '0.5rem',
                        padding: '0.75rem',
                        color: 'white',
                        fontSize: '1rem'
                      }}
                    >
                      <option value="low">Low Priority</option>
                      <option value="medium">Medium Priority</option>
                      <option value="high">High Priority</option>
                    </select>
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500', color: 'white' }}>
                      Due Date (Optional)
                    </label>
                    <input
                      type="date"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      style={{
                        width: '100%',
                        background: 'rgba(255, 255, 255, 0.1)',
                        border: 'none',
                        borderRadius: '0.5rem',
                        padding: '0.75rem',
                        color: 'white',
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <button
                    type="submit"
                    style={{
                      width: '100%',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none',
                      borderRadius: '0.5rem',
                      padding: '0.75rem',
                      color: 'white',
                      fontSize: '1rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'transform 0.2s'
                    }}
                    onMouseOver={(e) => (e.target as HTMLElement).style.transform = 'scale(1.02)'}
                    onMouseOut={(e) => (e.target as HTMLElement).style.transform = 'scale(1)'}
                  >
                    ➕ Create Task
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
