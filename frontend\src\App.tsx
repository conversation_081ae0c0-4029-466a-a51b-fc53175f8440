import { useState, useEffect } from 'react';
import axios from 'axios';

// Import advanced features
import { pwaManager, addPWAEventListener, removePWAEventListener, taskNotificationManager } from './utils/pwa';
import { themeManager, setTheme, getCurrentTheme, getThemeColors } from './utils/theme';
import { analytics, trackPageView, trackLogin, trackLogout, trackRegistration, trackTaskCreated, trackTaskCompleted, trackTaskDeleted, identifyUser } from './utils/analytics';

interface User {
  FirstName: string;
  LastName: string;
  email: string;
  id?: string;
}

interface Task {
  _id: string;
  taskName: string;
  taskDescription: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
}

function App() {
  const [currentPage, setCurrentPage] = useState<'landing' | 'login' | 'register' | 'dashboard'>('landing');
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Login form state
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');

  // Register form state
  const [registerFirstName, setRegisterFirstName] = useState('');
  const [registerLastName, setRegisterLastName] = useState('');
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');

  // Task management state
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskLoading, setTaskLoading] = useState(false);
  const [showCreateTask, setShowCreateTask] = useState(false);

  // Task form state
  const [taskName, setTaskName] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [taskPriority, setTaskPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [taskDueDate, setTaskDueDate] = useState('');

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'in-progress' | 'completed'>('all');
  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high'>('all');

  // Advanced features state
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [currentTheme, setCurrentTheme] = useState(getCurrentTheme());
  const [showThemeSelector, setShowThemeSelector] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);

  // Check if user is already logged in
  useEffect(() => {
    if (token) {
      fetchUserData();
    }

    // Initialize advanced features
    initializeAdvancedFeatures();
  }, [token]);

  const initializeAdvancedFeatures = () => {
    // Track page view
    trackPageView('app', 'TaskDrive App');

    // Setup PWA event listeners
    addPWAEventListener('pwa-install-available', () => setShowInstallPrompt(true));
    addPWAEventListener('pwa-install-completed', () => setShowInstallPrompt(false));
    addPWAEventListener('network-offline', () => setIsOffline(true));
    addPWAEventListener('network-online', () => setIsOffline(false));
    addPWAEventListener('shortcut-create-task', () => setShowCreateTask(true));

    // Setup theme change listener
    addPWAEventListener('theme-changed', () => setCurrentTheme(getCurrentTheme()));

    // Check notification permission
    if ('Notification' in window) {
      setNotificationsEnabled(Notification.permission === 'granted');
    }

    // Setup overdue task checking
    taskNotificationManager.scheduleOverdueCheck();
    addPWAEventListener('check-overdue-tasks', checkOverdueTasks);

    // Cleanup function
    return () => {
      removePWAEventListener('pwa-install-available', () => setShowInstallPrompt(true));
      removePWAEventListener('pwa-install-completed', () => setShowInstallPrompt(false));
      removePWAEventListener('network-offline', () => setIsOffline(true));
      removePWAEventListener('network-online', () => setIsOffline(false));
      removePWAEventListener('shortcut-create-task', () => setShowCreateTask(true));
      removePWAEventListener('theme-changed', () => setCurrentTheme(getCurrentTheme()));
      removePWAEventListener('check-overdue-tasks', checkOverdueTasks);
    };
  };

  const fetchUserData = async () => {
    try {
      const response = await axios.get('http://localhost:5000/api/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.data.success) {
        setUser(response.data.user);
        setCurrentPage('dashboard');
        fetchTasks(); // Fetch tasks when user data is loaded
      }
    } catch (error) {
      console.error('Failed to fetch user data:', error);
      localStorage.removeItem('token');
      setToken(null);
    }
  };

  const fetchTasks = async () => {
    if (!token) return;

    setTaskLoading(true);
    try {
      const response = await axios.get('http://localhost:5000/api/tasks', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.data.success) {
        setTasks(response.data.tasks);
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
    } finally {
      setTaskLoading(false);
    }
  };

  const createTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!taskName.trim() || !taskDescription.trim()) return;

    setTaskLoading(true);
    try {
      const response = await axios.post('http://localhost:5000/api/tasks', {
        taskName,
        taskDescription,
        priority: taskPriority,
        dueDate: taskDueDate || undefined
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setTasks([response.data.task, ...tasks]);
        trackTaskCreated(response.data.task);
        setTaskName('');
        setTaskDescription('');
        setTaskPriority('medium');
        setTaskDueDate('');
        setShowCreateTask(false);
      }
    } catch (error) {
      console.error('Failed to create task:', error);
    } finally {
      setTaskLoading(false);
    }
  };

  const toggleTaskStatus = async (taskId: string) => {
    try {
      const response = await axios.patch(`http://localhost:5000/api/tasks/${taskId}/toggle`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setTasks(tasks.map(task =>
          task._id === taskId ? response.data.task : task
        ));

        // Track completion if task was marked as completed
        if (response.data.task.status === 'completed') {
          trackTaskCompleted(response.data.task);
        }
      }
    } catch (error) {
      console.error('Failed to toggle task status:', error);
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
      const response = await axios.delete(`http://localhost:5000/api/tasks/${taskId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        const deletedTask = tasks.find(task => task._id === taskId);
        if (deletedTask) {
          trackTaskDeleted(deletedTask);
        }
        setTasks(tasks.filter(task => task._id !== taskId));
      }
    } catch (error) {
      console.error('Failed to delete task:', error);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post('http://localhost:5000/api/auth/login', {
        email: loginEmail,
        password: loginPassword
      });

      if (response.data.success) {
        setToken(response.data.token);
        setUser(response.data.user);
        localStorage.setItem('token', response.data.token);
        setCurrentPage('dashboard');

        // Analytics and identification
        identifyUser(response.data.user.id, {
          firstName: response.data.user.FirstName,
          lastName: response.data.user.LastName,
          email: response.data.user.email
        });
        trackLogin('email');

        // Clear form
        setLoginEmail('');
        setLoginPassword('');
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post('http://localhost:5000/api/auth/register', {
        FirstName: registerFirstName,
        LastName: registerLastName,
        email: registerEmail,
        password: registerPassword
      });

      if (response.data.success) {
        setToken(response.data.token);
        setUser(response.data.user);
        localStorage.setItem('token', response.data.token);
        setCurrentPage('dashboard');

        // Analytics and identification
        identifyUser(response.data.user.id, {
          firstName: response.data.user.FirstName,
          lastName: response.data.user.LastName,
          email: response.data.user.email
        });
        trackRegistration('email');

        // Clear form
        setRegisterFirstName('');
        setRegisterLastName('');
        setRegisterEmail('');
        setRegisterPassword('');
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    trackLogout();
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
    setCurrentPage('landing');
    setTasks([]);
  };

  // Advanced features helper functions
  const checkOverdueTasks = () => {
    const now = new Date();
    const overdueTasks = tasks.filter(task => {
      if (!task.dueDate || task.status === 'completed') return false;
      return new Date(task.dueDate) < now;
    });

    if (overdueTasks.length > 0) {
      taskNotificationManager.showOverdueNotification(overdueTasks);
    }
  };

  const handleInstallPWA = async () => {
    const installed = await pwaManager.promptInstall();
    if (installed) {
      setShowInstallPrompt(false);
    }
  };

  const handleThemeChange = (themeName: string) => {
    setTheme(themeName as any);
    setShowThemeSelector(false);
  };

  const requestNotificationPermission = async () => {
    const granted = await pwaManager.requestNotificationPermission();
    setNotificationsEnabled(granted);
    if (granted) {
      pwaManager.showNotification({
        title: '🔔 Notifications Enabled',
        body: 'You will now receive task reminders and updates!'
      });
    }
  };

  const LandingPage = () => (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: 'Inter, Arial, sans-serif'
    }}>
      {/* Navigation */}
      <nav style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        position: 'fixed',
        width: '100%',
        zIndex: 50,
        top: 0,
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto', 
          padding: '0 2rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '4rem'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <h1 style={{ 
              fontSize: '1.75rem', 
              fontWeight: 'bold', 
              color: 'white', 
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🚀 TaskDrive
            </h1>
          </div>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button
              onClick={() => setCurrentPage('login')}
              style={{
                background: 'transparent',
                border: '2px solid white',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.75rem',
                cursor: 'pointer',
                fontWeight: '600',
                fontSize: '1rem',
                transition: 'all 0.3s ease'
              }}
              onMouseOver={(e) => {
                (e.target as HTMLElement).style.background = 'white';
                (e.target as HTMLElement).style.color = '#667eea';
              }}
              onMouseOut={(e) => {
                (e.target as HTMLElement).style.background = 'transparent';
                (e.target as HTMLElement).style.color = 'white';
              }}
            >
              Login
            </button>
            <button
              onClick={() => setCurrentPage('register')}
              style={{
                background: 'white',
                border: '2px solid white',
                color: '#667eea',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.75rem',
                cursor: 'pointer',
                fontWeight: '600',
                fontSize: '1rem',
                transition: 'all 0.3s ease'
              }}
              onMouseOver={(e) => {
                (e.target as HTMLElement).style.background = 'transparent';
                (e.target as HTMLElement).style.color = 'white';
              }}
              onMouseOut={(e) => {
                (e.target as HTMLElement).style.background = 'white';
                (e.target as HTMLElement).style.color = '#667eea';
              }}
            >
              Register
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div style={{ 
        paddingTop: '6rem',
        display: 'flex',
        alignItems: 'center',
        minHeight: '100vh',
        padding: '6rem 2rem 2rem'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', width: '100%' }}>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: '1fr 1fr', 
            gap: '4rem',
            alignItems: 'center'
          }}>
            {/* Left Content */}
            <div>
              <h1 style={{
                fontSize: '4rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '1.5rem',
                lineHeight: '1.1'
              }}>
                Organize Your Tasks Like Never Before
              </h1>
              <p style={{
                fontSize: '1.25rem',
                color: 'rgba(255, 255, 255, 0.9)',
                marginBottom: '2rem',
                lineHeight: '1.6'
              }}>
                TaskDrive helps you stay productive and organized with powerful task management features. 
                Create, track, and complete your goals with style and efficiency.
              </p>
              <div style={{ display: 'flex', gap: '1rem', marginBottom: '3rem' }}>
                <button
                  onClick={() => setCurrentPage('register')}
                  style={{
                    background: 'white',
                    color: '#667eea',
                    border: 'none',
                    padding: '1rem 2rem',
                    borderRadius: '0.75rem',
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
                  }}
                  onMouseOver={(e) => {
                    (e.target as HTMLElement).style.transform = 'translateY(-2px)';
                    (e.target as HTMLElement).style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
                  }}
                  onMouseOut={(e) => {
                    (e.target as HTMLElement).style.transform = 'translateY(0)';
                    (e.target as HTMLElement).style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                  }}
                >
                  Get Started Free
                </button>
                <button
                  onClick={() => setCurrentPage('login')}
                  style={{
                    background: 'transparent',
                    color: 'white',
                    border: '2px solid white',
                    padding: '1rem 2rem',
                    borderRadius: '0.75rem',
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseOver={(e) => {
                    (e.target as HTMLElement).style.background = 'rgba(255, 255, 255, 0.1)';
                  }}
                  onMouseOut={(e) => {
                    (e.target as HTMLElement).style.background = 'transparent';
                  }}
                >
                  Sign In
                </button>
              </div>
              
              {/* Features */}
              <div style={{ display: 'flex', gap: '2rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <span style={{ fontSize: '1.5rem' }}>✅</span>
                  <span style={{ color: 'white', fontWeight: '500' }}>Easy Task Management</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <span style={{ fontSize: '1.5rem' }}>⚡</span>
                  <span style={{ color: 'white', fontWeight: '500' }}>Real-time Updates</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <span style={{ fontSize: '1.5rem' }}>🎯</span>
                  <span style={{ color: 'white', fontWeight: '500' }}>Priority Management</span>
                </div>
              </div>
            </div>

            {/* Right Content - Feature Preview */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1.5rem',
              padding: '2rem',
              border: '1px solid rgba(255, 255, 255, 0.2)'
            }}>
              <h3 style={{ 
                color: 'white', 
                fontSize: '1.5rem', 
                marginBottom: '1.5rem',
                fontWeight: '600'
              }}>
                📊 Task Dashboard Preview
              </h3>
              
              {/* Mock Stats */}
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(3, 1fr)', 
                gap: '1rem',
                marginBottom: '2rem'
              }}>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'white' }}>12</div>
                  <div style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.8)' }}>Total Tasks</div>
                </div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>8</div>
                  <div style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.8)' }}>Completed</div>
                </div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>4</div>
                  <div style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.8)' }}>Pending</div>
                </div>
              </div>

              {/* Mock Task List */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  borderLeft: '4px solid #10b981'
                }}>
                  <div style={{ color: 'white', fontWeight: '600', marginBottom: '0.25rem' }}>
                    Complete project proposal
                  </div>
                  <div style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.7)' }}>
                    High Priority • Completed
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  borderLeft: '4px solid #f59e0b'
                }}>
                  <div style={{ color: 'white', fontWeight: '600', marginBottom: '0.25rem' }}>
                    Review team feedback
                  </div>
                  <div style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.7)' }}>
                    Medium Priority • Pending
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  borderLeft: '4px solid #3b82f6'
                }}>
                  <div style={{ color: 'white', fontWeight: '600', marginBottom: '0.25rem' }}>
                    Update documentation
                  </div>
                  <div style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.7)' }}>
                    Low Priority • In Progress
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const LoginPage = () => (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Inter, Arial, sans-serif'
    }}>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: '1.5rem',
        padding: '3rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        width: '100%',
        maxWidth: '400px'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <h1 style={{ color: 'white', fontSize: '2rem', fontWeight: 'bold', margin: 0 }}>
            🚀 TaskDrive
          </h1>
          <p style={{ color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
            Welcome back! Sign in to your account
          </p>
        </div>

        {error && (
          <div style={{
            background: 'rgba(239, 68, 68, 0.1)',
            border: '1px solid rgba(239, 68, 68, 0.3)',
            borderRadius: '0.75rem',
            padding: '1rem',
            marginBottom: '1.5rem',
            color: '#fca5a5',
            textAlign: 'center'
          }}>
            {error}
          </div>
        )}

        <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
          <div>
            <label style={{ color: 'white', fontWeight: '500', marginBottom: '0.5rem', display: 'block' }}>
              Email
            </label>
            <input
              type="email"
              value={loginEmail}
              onChange={(e) => setLoginEmail(e.target.value)}
              placeholder="Enter your email"
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '0.75rem',
                border: '2px solid rgba(255, 255, 255, 0.2)',
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                fontSize: '1rem'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', fontWeight: '500', marginBottom: '0.5rem', display: 'block' }}>
              Password
            </label>
            <input
              type="password"
              value={loginPassword}
              onChange={(e) => setLoginPassword(e.target.value)}
              placeholder="Enter your password"
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '0.75rem',
                border: '2px solid rgba(255, 255, 255, 0.2)',
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                fontSize: '1rem'
              }}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            style={{
              background: loading ? 'rgba(255, 255, 255, 0.5)' : 'white',
              color: '#667eea',
              border: 'none',
              padding: '1rem',
              borderRadius: '0.75rem',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            {loading ? (
              <>
                <div style={{
                  width: '20px',
                  height: '20px',
                  border: '2px solid #667eea',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                Signing In...
              </>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          <p style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            Don't have an account?{' '}
            <button
              onClick={() => setCurrentPage('register')}
              style={{
                background: 'none',
                border: 'none',
                color: 'white',
                textDecoration: 'underline',
                cursor: 'pointer',
                fontWeight: '600'
              }}
            >
              Sign up
            </button>
          </p>
          <button
            onClick={() => setCurrentPage('landing')}
            style={{
              background: 'none',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.8)',
              cursor: 'pointer',
              marginTop: '1rem'
            }}
          >
            ← Back to home
          </button>
        </div>
      </div>
    </div>
  );

  const RegisterPage = () => (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Inter, Arial, sans-serif'
    }}>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: '1.5rem',
        padding: '3rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        width: '100%',
        maxWidth: '400px'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <h1 style={{ color: 'white', fontSize: '2rem', fontWeight: 'bold', margin: 0 }}>
            🚀 TaskDrive
          </h1>
          <p style={{ color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
            Create your account and start organizing
          </p>
        </div>

        {error && (
          <div style={{
            background: 'rgba(239, 68, 68, 0.1)',
            border: '1px solid rgba(239, 68, 68, 0.3)',
            borderRadius: '0.75rem',
            padding: '1rem',
            marginBottom: '1.5rem',
            color: '#fca5a5',
            textAlign: 'center'
          }}>
            {error}
          </div>
        )}

        <form onSubmit={handleRegister} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div>
              <label style={{ color: 'white', fontWeight: '500', marginBottom: '0.5rem', display: 'block' }}>
                First Name
              </label>
              <input
                type="text"
                value={registerFirstName}
                onChange={(e) => setRegisterFirstName(e.target.value)}
                placeholder="John"
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '0.75rem',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '1rem'
                }}
              />
            </div>
            <div>
              <label style={{ color: 'white', fontWeight: '500', marginBottom: '0.5rem', display: 'block' }}>
                Last Name
              </label>
              <input
                type="text"
                value={registerLastName}
                onChange={(e) => setRegisterLastName(e.target.value)}
                placeholder="Doe"
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '0.75rem',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '1rem'
                }}
              />
            </div>
          </div>

          <div>
            <label style={{ color: 'white', fontWeight: '500', marginBottom: '0.5rem', display: 'block' }}>
              Email
            </label>
            <input
              type="email"
              value={registerEmail}
              onChange={(e) => setRegisterEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '0.75rem',
                border: '2px solid rgba(255, 255, 255, 0.2)',
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                fontSize: '1rem'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', fontWeight: '500', marginBottom: '0.5rem', display: 'block' }}>
              Password
            </label>
            <input
              type="password"
              value={registerPassword}
              onChange={(e) => setRegisterPassword(e.target.value)}
              placeholder="Create a strong password"
              required
              minLength={6}
              style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '0.75rem',
                border: '2px solid rgba(255, 255, 255, 0.2)',
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                fontSize: '1rem'
              }}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            style={{
              background: loading ? 'rgba(255, 255, 255, 0.5)' : 'white',
              color: '#667eea',
              border: 'none',
              padding: '1rem',
              borderRadius: '0.75rem',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            {loading ? (
              <>
                <div style={{
                  width: '20px',
                  height: '20px',
                  border: '2px solid #667eea',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                Creating Account...
              </>
            ) : (
              'Create Account'
            )}
          </button>
        </form>

        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          <p style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            Already have an account?{' '}
            <button
              onClick={() => setCurrentPage('login')}
              style={{
                background: 'none',
                border: 'none',
                color: 'white',
                textDecoration: 'underline',
                cursor: 'pointer',
                fontWeight: '600'
              }}
            >
              Sign in
            </button>
          </p>
          <button
            onClick={() => setCurrentPage('landing')}
            style={{
              background: 'none',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.8)',
              cursor: 'pointer',
              marginTop: '1rem'
            }}
          >
            ← Back to home
          </button>
        </div>
      </div>
    </div>
  );

  // Filter tasks based on search and filters
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.taskName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.taskDescription.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || task.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || task.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Calculate statistics
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(task => task.status === 'completed').length;
  const pendingTasks = tasks.filter(task => task.status === 'pending').length;
  const inProgressTasks = tasks.filter(task => task.status === 'in-progress').length;

  const Dashboard = () => (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: 'Inter, Arial, sans-serif'
    }}>
      {/* Navigation */}
      <nav style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        position: 'fixed',
        width: '100%',
        zIndex: 50,
        top: 0,
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 2rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '4rem'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h1 style={{
              fontSize: '1.75rem',
              fontWeight: 'bold',
              color: 'white',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🚀 TaskDrive
            </h1>

            {/* Status Indicators */}
            {isOffline && (
              <div style={{
                background: 'rgba(239, 68, 68, 0.2)',
                color: '#fca5a5',
                padding: '0.25rem 0.75rem',
                borderRadius: '9999px',
                fontSize: '0.75rem',
                fontWeight: '500'
              }}>
                📡 Offline
              </div>
            )}

            {showInstallPrompt && (
              <button
                onClick={handleInstallPWA}
                style={{
                  background: 'rgba(16, 185, 129, 0.2)',
                  color: '#6ee7b7',
                  border: '1px solid rgba(16, 185, 129, 0.3)',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '9999px',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                📱 Install App
              </button>
            )}
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            {/* Theme Selector */}
            <button
              onClick={() => setShowThemeSelector(!showThemeSelector)}
              style={{
                background: 'transparent',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                color: 'white',
                padding: '0.5rem',
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontSize: '1rem',
                transition: 'all 0.3s ease'
              }}
              title="Change Theme"
            >
              🎨
            </button>

            {/* Notifications Toggle */}
            <button
              onClick={requestNotificationPermission}
              style={{
                background: notificationsEnabled ? 'rgba(16, 185, 129, 0.2)' : 'transparent',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                color: 'white',
                padding: '0.5rem',
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontSize: '1rem',
                transition: 'all 0.3s ease'
              }}
              title={notificationsEnabled ? 'Notifications Enabled' : 'Enable Notifications'}
            >
              {notificationsEnabled ? '🔔' : '🔕'}
            </button>

            <span style={{ color: 'white', fontWeight: '500' }}>
              Welcome, {user?.FirstName}!
            </span>
            <button
              onClick={handleLogout}
              style={{
                background: 'transparent',
                border: '2px solid white',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontWeight: '500',
                fontSize: '0.875rem',
                transition: 'all 0.3s ease'
              }}
              onMouseOver={(e) => {
                (e.target as HTMLElement).style.background = 'white';
                (e.target as HTMLElement).style.color = '#667eea';
              }}
              onMouseOut={(e) => {
                (e.target as HTMLElement).style.background = 'transparent';
                (e.target as HTMLElement).style.color = 'white';
              }}
            >
              Logout
            </button>
          </div>
        </div>

        {/* Theme Selector Dropdown */}
        {showThemeSelector && (
          <div style={{
            position: 'absolute',
            top: '100%',
            right: '2rem',
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            borderRadius: '0.75rem',
            padding: '1rem',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            minWidth: '200px'
          }}>
            <h4 style={{ color: 'white', margin: '0 0 0.5rem 0', fontSize: '0.875rem' }}>Choose Theme</h4>
            {['light', 'dark', 'blue', 'green', 'purple'].map(theme => (
              <button
                key={theme}
                onClick={() => handleThemeChange(theme)}
                style={{
                  display: 'block',
                  width: '100%',
                  background: currentTheme === theme ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                  border: 'none',
                  color: 'white',
                  padding: '0.5rem',
                  borderRadius: '0.5rem',
                  cursor: 'pointer',
                  fontSize: '0.875rem',
                  marginBottom: '0.25rem',
                  textAlign: 'left',
                  textTransform: 'capitalize'
                }}
              >
                {theme === 'light' && '☀️'}
                {theme === 'dark' && '🌙'}
                {theme === 'blue' && '💙'}
                {theme === 'green' && '💚'}
                {theme === 'purple' && '💜'}
                {theme}
              </button>
            ))}
          </div>
        )}
      </nav>

      {/* Dashboard Content */}
      <div style={{ paddingTop: '6rem', padding: '6rem 2rem 2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>

          {/* Welcome Header */}
          <div style={{ marginBottom: '2rem', textAlign: 'center' }}>
            <h2 style={{
              color: 'white',
              fontSize: '2.5rem',
              marginBottom: '0.5rem',
              fontWeight: 'bold'
            }}>
              Welcome back, {user?.FirstName}! 👋
            </h2>
            <p style={{
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: '1.125rem'
            }}>
              Let's get things done today
            </p>
          </div>

          {/* Statistics Cards */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: 'white', marginBottom: '0.5rem' }}>
                {totalTasks}
              </div>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)', fontWeight: '500' }}>
                📋 Total Tasks
              </div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#10b981', marginBottom: '0.5rem' }}>
                {completedTasks}
              </div>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)', fontWeight: '500' }}>
                ✅ Completed
              </div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#f59e0b', marginBottom: '0.5rem' }}>
                {inProgressTasks}
              </div>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)', fontWeight: '500' }}>
                🔄 In Progress
              </div>
            </div>

            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#ef4444', marginBottom: '0.5rem' }}>
                {pendingTasks}
              </div>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)', fontWeight: '500' }}>
                ⏳ Pending
              </div>
            </div>
          </div>

          {/* Controls Section */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            borderRadius: '1rem',
            padding: '1.5rem',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            marginBottom: '2rem'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem',
              flexWrap: 'wrap',
              gap: '1rem'
            }}>
              <h3 style={{ color: 'white', fontSize: '1.5rem', fontWeight: '600', margin: 0 }}>
                📝 Task Management
              </h3>
              <button
                onClick={() => setShowCreateTask(!showCreateTask)}
                style={{
                  background: 'white',
                  color: '#667eea',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.75rem',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
                onMouseOver={(e) => {
                  (e.target as HTMLElement).style.transform = 'translateY(-2px)';
                  (e.target as HTMLElement).style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
                }}
                onMouseOut={(e) => {
                  (e.target as HTMLElement).style.transform = 'translateY(0)';
                  (e.target as HTMLElement).style.boxShadow = 'none';
                }}
              >
                {showCreateTask ? '❌ Cancel' : '➕ Add New Task'}
              </button>
            </div>

            {/* Search and Filters */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem',
              marginBottom: showCreateTask ? '1.5rem' : '0'
            }}>
              <input
                type="text"
                placeholder="🔍 Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  padding: '0.75rem',
                  borderRadius: '0.75rem',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '1rem'
                }}
              />

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                style={{
                  padding: '0.75rem',
                  borderRadius: '0.75rem',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '1rem'
                }}
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>

              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value as any)}
                style={{
                  padding: '0.75rem',
                  borderRadius: '0.75rem',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '1rem'
                }}
              >
                <option value="all">All Priority</option>
                <option value="low">Low Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="high">High Priority</option>
              </select>
            </div>

            {/* Create Task Form */}
            {showCreateTask && (
              <form onSubmit={createTask} style={{
                background: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '0.75rem',
                padding: '1.5rem',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}>
                <h4 style={{ color: 'white', marginBottom: '1rem', fontSize: '1.25rem' }}>
                  ✨ Create New Task
                </h4>

                <div style={{ display: 'grid', gap: '1rem' }}>
                  <input
                    type="text"
                    placeholder="Task name..."
                    value={taskName}
                    onChange={(e) => setTaskName(e.target.value)}
                    required
                    style={{
                      padding: '0.75rem',
                      borderRadius: '0.75rem',
                      border: '2px solid rgba(255, 255, 255, 0.2)',
                      background: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      fontSize: '1rem'
                    }}
                  />

                  <textarea
                    placeholder="Task description..."
                    value={taskDescription}
                    onChange={(e) => setTaskDescription(e.target.value)}
                    required
                    rows={3}
                    style={{
                      padding: '0.75rem',
                      borderRadius: '0.75rem',
                      border: '2px solid rgba(255, 255, 255, 0.2)',
                      background: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      fontSize: '1rem',
                      resize: 'vertical'
                    }}
                  />

                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                    <select
                      value={taskPriority}
                      onChange={(e) => setTaskPriority(e.target.value as any)}
                      style={{
                        padding: '0.75rem',
                        borderRadius: '0.75rem',
                        border: '2px solid rgba(255, 255, 255, 0.2)',
                        background: 'rgba(255, 255, 255, 0.1)',
                        color: 'white',
                        fontSize: '1rem'
                      }}
                    >
                      <option value="low">🟢 Low Priority</option>
                      <option value="medium">🟡 Medium Priority</option>
                      <option value="high">🔴 High Priority</option>
                    </select>

                    <input
                      type="date"
                      value={taskDueDate}
                      onChange={(e) => setTaskDueDate(e.target.value)}
                      style={{
                        padding: '0.75rem',
                        borderRadius: '0.75rem',
                        border: '2px solid rgba(255, 255, 255, 0.2)',
                        background: 'rgba(255, 255, 255, 0.1)',
                        color: 'white',
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={taskLoading}
                    style={{
                      background: taskLoading ? 'rgba(255, 255, 255, 0.5)' : 'white',
                      color: '#667eea',
                      border: 'none',
                      padding: '1rem',
                      borderRadius: '0.75rem',
                      fontSize: '1rem',
                      fontWeight: '600',
                      cursor: taskLoading ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    {taskLoading ? (
                      <>
                        <div style={{
                          width: '20px',
                          height: '20px',
                          border: '2px solid #667eea',
                          borderTop: '2px solid transparent',
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite'
                        }}></div>
                        Creating...
                      </>
                    ) : (
                      '🚀 Create Task'
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>

          {/* Task List */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            borderRadius: '1rem',
            padding: '1.5rem',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}>
            <h3 style={{ color: 'white', fontSize: '1.5rem', fontWeight: '600', marginBottom: '1.5rem' }}>
              📋 Your Tasks ({filteredTasks.length})
            </h3>

            {taskLoading ? (
              <div style={{ textAlign: 'center', padding: '2rem' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  border: '4px solid rgba(255, 255, 255, 0.3)',
                  borderTop: '4px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto 1rem'
                }}></div>
                <p style={{ color: 'rgba(255, 255, 255, 0.8)' }}>Loading tasks...</p>
              </div>
            ) : filteredTasks.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '3rem' }}>
                <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📝</div>
                <h4 style={{ color: 'white', marginBottom: '0.5rem' }}>
                  {tasks.length === 0 ? 'No tasks yet!' : 'No tasks match your filters'}
                </h4>
                <p style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  {tasks.length === 0
                    ? 'Create your first task to get started'
                    : 'Try adjusting your search or filters'
                  }
                </p>
              </div>
            ) : (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {filteredTasks.map((task) => (
                  <div
                    key={task._id}
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      borderRadius: '0.75rem',
                      padding: '1.5rem',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderLeft: `4px solid ${
                        task.priority === 'high' ? '#ef4444' :
                        task.priority === 'medium' ? '#f59e0b' : '#10b981'
                      }`,
                      transition: 'all 0.3s ease'
                    }}
                    onMouseOver={(e) => {
                      (e.target as HTMLElement).style.transform = 'translateY(-2px)';
                      (e.target as HTMLElement).style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
                    }}
                    onMouseOut={(e) => {
                      (e.target as HTMLElement).style.transform = 'translateY(0)';
                      (e.target as HTMLElement).style.boxShadow = 'none';
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                      <div style={{ flex: 1 }}>
                        <h4 style={{
                          color: 'white',
                          fontSize: '1.25rem',
                          fontWeight: '600',
                          marginBottom: '0.5rem',
                          textDecoration: task.status === 'completed' ? 'line-through' : 'none',
                          opacity: task.status === 'completed' ? 0.7 : 1
                        }}>
                          {task.taskName}
                        </h4>
                        <p style={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          marginBottom: '1rem',
                          lineHeight: '1.5'
                        }}>
                          {task.taskDescription}
                        </p>
                      </div>

                      <div style={{ display: 'flex', gap: '0.5rem', marginLeft: '1rem' }}>
                        <button
                          onClick={() => toggleTaskStatus(task._id)}
                          style={{
                            background: task.status === 'completed' ? '#10b981' : 'rgba(255, 255, 255, 0.2)',
                            border: 'none',
                            borderRadius: '0.5rem',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            fontSize: '1.25rem',
                            transition: 'all 0.3s ease'
                          }}
                          title={task.status === 'completed' ? 'Mark as pending' : 'Mark as completed'}
                        >
                          {task.status === 'completed' ? '✅' : '⭕'}
                        </button>

                        <button
                          onClick={() => deleteTask(task._id)}
                          style={{
                            background: 'rgba(239, 68, 68, 0.2)',
                            border: 'none',
                            borderRadius: '0.5rem',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            fontSize: '1.25rem',
                            transition: 'all 0.3s ease'
                          }}
                          title="Delete task"
                          onMouseOver={(e) => {
                            (e.target as HTMLElement).style.background = 'rgba(239, 68, 68, 0.4)';
                          }}
                          onMouseOut={(e) => {
                            (e.target as HTMLElement).style.background = 'rgba(239, 68, 68, 0.2)';
                          }}
                        >
                          🗑️
                        </button>
                      </div>
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '1rem' }}>
                      <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                        <span style={{
                          background: task.priority === 'high' ? 'rgba(239, 68, 68, 0.2)' :
                                     task.priority === 'medium' ? 'rgba(245, 158, 11, 0.2)' : 'rgba(16, 185, 129, 0.2)',
                          color: task.priority === 'high' ? '#fca5a5' :
                                task.priority === 'medium' ? '#fcd34d' : '#6ee7b7',
                          padding: '0.25rem 0.75rem',
                          borderRadius: '9999px',
                          fontSize: '0.875rem',
                          fontWeight: '500'
                        }}>
                          {task.priority === 'high' ? '🔴 High' :
                           task.priority === 'medium' ? '🟡 Medium' : '🟢 Low'} Priority
                        </span>

                        <span style={{
                          background: task.status === 'completed' ? 'rgba(16, 185, 129, 0.2)' :
                                     task.status === 'in-progress' ? 'rgba(245, 158, 11, 0.2)' : 'rgba(239, 68, 68, 0.2)',
                          color: task.status === 'completed' ? '#6ee7b7' :
                                task.status === 'in-progress' ? '#fcd34d' : '#fca5a5',
                          padding: '0.25rem 0.75rem',
                          borderRadius: '9999px',
                          fontSize: '0.875rem',
                          fontWeight: '500'
                        }}>
                          {task.status === 'completed' ? '✅ Completed' :
                           task.status === 'in-progress' ? '🔄 In Progress' : '⏳ Pending'}
                        </span>
                      </div>

                      <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.6)' }}>
                        {task.dueDate && (
                          <span>📅 Due: {new Date(task.dueDate).toLocaleDateString()}</span>
                        )}
                        <span>📝 Created: {new Date(task.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
      {currentPage === 'landing' && <LandingPage />}
      {currentPage === 'login' && <LoginPage />}
      {currentPage === 'register' && <RegisterPage />}
      {currentPage === 'dashboard' && <Dashboard />}
    </div>
  );
}

export default App;
