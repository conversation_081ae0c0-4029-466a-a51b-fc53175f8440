import { useState, useEffect } from 'react';
import { Task, CreateTaskData, UpdateTaskData, TaskFilters, TaskSort, TaskStats } from '../types';
import { tasksAPI } from '../services/api';
import toast from 'react-hot-toast';

export const useTasks = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [stats, setStats] = useState<TaskStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters and sorting
  const [filters, setFilters] = useState<TaskFilters>({
    status: 'all',
    priority: 'all',
    search: '',
  });
  
  const [sort, setSort] = useState<TaskSort>({
    sortBy: 'createdAt',
    order: 'desc',
  });

  // Fetch tasks
  const fetchTasks = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const params = {
        ...(filters.status !== 'all' && { status: filters.status }),
        ...(filters.priority !== 'all' && { priority: filters.priority }),
        ...(filters.search && { search: filters.search }),
        sortBy: sort.sortBy,
        order: sort.order,
      };

      const response = await tasksAPI.getTasks(params);
      
      if (response.success) {
        setTasks(response.tasks);
        setStats(response.stats);
      } else {
        setError('Failed to fetch tasks');
      }
    } catch (error: any) {
      console.error('Fetch tasks error:', error);
      setError(error.response?.data?.message || 'Failed to fetch tasks');
    } finally {
      setIsLoading(false);
    }
  };

  // Create task
  const createTask = async (data: CreateTaskData): Promise<boolean> => {
    try {
      const response = await tasksAPI.createTask(data);
      
      if (response.success && response.data) {
        setTasks(prev => [response.data!, ...prev]);
        toast.success('Task created successfully!');
        
        // Update stats
        if (stats) {
          setStats(prev => prev ? {
            ...prev,
            totalTasks: prev.totalTasks + 1,
            pendingTasks: prev.pendingTasks + 1,
          } : null);
        }
        
        return true;
      } else {
        toast.error(response.message || 'Failed to create task');
        return false;
      }
    } catch (error: any) {
      console.error('Create task error:', error);
      toast.error(error.response?.data?.message || 'Failed to create task');
      return false;
    }
  };

  // Update task
  const updateTask = async (id: string, data: UpdateTaskData): Promise<boolean> => {
    try {
      const response = await tasksAPI.updateTask(id, data);
      
      if (response.success && response.data) {
        setTasks(prev => 
          prev.map(task => 
            task._id === id ? response.data! : task
          )
        );
        toast.success('Task updated successfully!');
        return true;
      } else {
        toast.error(response.message || 'Failed to update task');
        return false;
      }
    } catch (error: any) {
      console.error('Update task error:', error);
      toast.error(error.response?.data?.message || 'Failed to update task');
      return false;
    }
  };

  // Toggle task status
  const toggleTaskStatus = async (id: string): Promise<boolean> => {
    try {
      const response = await tasksAPI.toggleTaskStatus(id);
      
      if (response.success && response.data) {
        setTasks(prev => 
          prev.map(task => 
            task._id === id ? response.data! : task
          )
        );
        toast.success('Task status updated!');
        
        // Refresh stats
        fetchTasks();
        return true;
      } else {
        toast.error(response.message || 'Failed to update task status');
        return false;
      }
    } catch (error: any) {
      console.error('Toggle task error:', error);
      toast.error(error.response?.data?.message || 'Failed to update task status');
      return false;
    }
  };

  // Delete task
  const deleteTask = async (id: string): Promise<boolean> => {
    try {
      const response = await tasksAPI.deleteTask(id);
      
      if (response.success) {
        setTasks(prev => prev.filter(task => task._id !== id));
        toast.success('Task deleted successfully!');
        
        // Update stats
        if (stats) {
          setStats(prev => prev ? {
            ...prev,
            totalTasks: prev.totalTasks - 1,
          } : null);
        }
        
        return true;
      } else {
        toast.error(response.message || 'Failed to delete task');
        return false;
      }
    } catch (error: any) {
      console.error('Delete task error:', error);
      toast.error(error.response?.data?.message || 'Failed to delete task');
      return false;
    }
  };

  // Update filters
  const updateFilters = (newFilters: Partial<TaskFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Update sort
  const updateSort = (newSort: Partial<TaskSort>) => {
    setSort(prev => ({ ...prev, ...newSort }));
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      status: 'all',
      priority: 'all',
      search: '',
    });
  };

  // Refresh tasks
  const refreshTasks = () => {
    fetchTasks();
  };

  // Effect to fetch tasks when filters or sort change
  useEffect(() => {
    fetchTasks();
  }, [filters, sort]);

  return {
    // Data
    tasks,
    stats,
    isLoading,
    error,
    filters,
    sort,
    
    // Actions
    createTask,
    updateTask,
    toggleTaskStatus,
    deleteTask,
    updateFilters,
    updateSort,
    clearFilters,
    refreshTasks,
    fetchTasks,
  };
};
