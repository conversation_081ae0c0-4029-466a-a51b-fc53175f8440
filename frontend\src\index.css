@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Two-Color Theme: Deep Blue (#1e3a8a) & Pure White (#ffffff) */

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-secondary-50 text-primary-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Glass Effects - Two Color Theme */
  .glass {
    @apply bg-secondary-50/90 backdrop-blur-xl border border-primary-200/20 shadow-elegant;
  }

  .glass-primary {
    @apply bg-primary-900/10 backdrop-blur-xl border border-primary-200/20 shadow-elegant;
  }

  /* Buttons - Two Color Theme */
  .btn-primary {
    @apply bg-primary-900 text-secondary-50 px-6 py-3 rounded-xl font-semibold font-display
           transition-all duration-300 ease-out border-2 border-primary-900
           hover:bg-secondary-50 hover:text-primary-900 hover:-translate-y-1 hover:shadow-elegant-lg
           active:translate-y-0 focus:outline-none focus:ring-4 focus:ring-primary-200;
  }

  .btn-secondary {
    @apply bg-secondary-50 text-primary-900 px-6 py-3 rounded-xl font-semibold font-display
           transition-all duration-300 ease-out border-2 border-primary-900
           hover:bg-primary-900 hover:text-secondary-50 hover:-translate-y-1 hover:shadow-elegant-lg
           active:translate-y-0 focus:outline-none focus:ring-4 focus:ring-primary-200;
  }

  .btn-ghost {
    @apply text-primary-900 px-6 py-3 rounded-xl font-medium
           transition-all duration-300 ease-out
           hover:bg-primary-50 hover:-translate-y-1
           active:translate-y-0 focus:outline-none focus:ring-4 focus:ring-primary-200;
  }

  /* Form Elements - Two Color Theme */
  .input-primary {
    @apply bg-secondary-50 border-2 border-primary-200 text-primary-900 rounded-xl px-4 py-3
           transition-all duration-300 ease-out font-medium
           focus:border-primary-900 focus:outline-none focus:ring-4 focus:ring-primary-100
           placeholder:text-primary-400;
  }

  .input-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-100;
  }

  /* Cards - Two Color Theme */
  .card {
    @apply glass rounded-2xl p-6 transition-all duration-300 ease-out;
  }

  .card-hover {
    @apply card cursor-pointer hover:-translate-y-2 hover:shadow-elegant-lg;
  }

  /* Task Priority - Two Color Theme */
  .priority-low {
    @apply border-l-4 border-primary-400;
  }

  .priority-medium {
    @apply border-l-4 border-primary-700;
  }

  .priority-high {
    @apply border-l-4 border-primary-900;
  }

  /* Status Badges - Two Color Theme */
  .status-badge {
    @apply px-3 py-1 rounded-full text-xs font-bold font-display uppercase tracking-wide;
  }

  .status-pending {
    @apply bg-primary-100 text-primary-700 border border-primary-200;
  }

  .status-in-progress {
    @apply bg-primary-600 text-secondary-50;
  }

  .status-completed {
    @apply bg-primary-900 text-secondary-50;
  }

  /* Navigation */
  .nav-primary {
    @apply glass fixed w-full z-50 top-0 border-b border-primary-200/20;
  }

  /* Loading Spinner - Two Color Theme */
  .spinner {
    @apply w-8 h-8 border-4 border-primary-200 border-t-primary-900 rounded-full animate-spin;
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-900 to-primary-600 bg-clip-text text-transparent font-display font-bold;
  }
}

@layer utilities {
  /* Animation Classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Custom Scrollbar - Two Color Theme */
  .scrollbar-custom {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.primary.900') theme('colors.primary.100');
  }

  .scrollbar-custom::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-custom::-webkit-scrollbar-track {
    @apply bg-primary-100 rounded-full;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb {
    @apply bg-primary-900 rounded-full;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-800;
  }
}

/* Additional Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-10px); 
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass {
    @apply backdrop-blur-lg;
  }
  
  .btn-primary, .btn-secondary {
    @apply px-4 py-2 text-sm;
  }
  
  .card {
    @apply p-4;
  }
}
