import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircleIcon, ClockIcon, PlayIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../hooks/useAuth';
import { useTasks } from '../hooks/useTasks';
import LoadingSpinner from '../components/LoadingSpinner';

const DashboardPage: React.FC = () => {
  const { user, logout } = useAuth();
  const { tasks, stats, isLoading } = useTasks();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  if (isLoading) {
    return <LoadingSpinner text="Loading your dashboard..." />;
  }

  return (
    <div className="bg-secondary-50 min-h-screen">
      {/* Navigation */}
      <motion.nav 
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="nav-primary"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <motion.div 
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <h1 className="text-2xl font-bold gradient-text">
                <CheckCircleIcon className="w-8 h-8 inline mr-2 text-primary-900" />
                TaskDrive
              </h1>
            </motion.div>
            
            <div className="flex items-center space-x-4">
              <span className="text-primary-900 font-medium">
                Welcome, {user?.FirstName}!
              </span>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={logout}
                className="btn-ghost"
              >
                Logout
              </motion.button>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Main Content */}
      <div className="pt-20 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Dashboard Header */}
          <motion.div 
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-8"
          >
            <motion.h2 variants={itemVariants} className="text-3xl md:text-4xl font-bold text-primary-900 mb-4 font-display">
              Your Task Dashboard
            </motion.h2>
            <motion.p variants={itemVariants} className="text-primary-700 text-lg">
              Manage your tasks efficiently and stay productive
            </motion.p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div 
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          >
            <motion.div variants={itemVariants} className="card-hover">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center mr-4">
                  <CheckCircleIcon className="w-6 h-6 text-secondary-50" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-primary-900">{stats?.totalTasks || 0}</h3>
                  <p className="text-primary-700">Total Tasks</p>
                </div>
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="card-hover">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mr-4">
                  <CheckCircleIcon className="w-6 h-6 text-secondary-50" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-primary-900">{stats?.completedTasks || 0}</h3>
                  <p className="text-primary-700">Completed</p>
                </div>
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="card-hover">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center mr-4">
                  <ClockIcon className="w-6 h-6 text-secondary-50" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-primary-900">{stats?.pendingTasks || 0}</h3>
                  <p className="text-primary-700">Pending</p>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Task Management Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Task List */}
            <div className="lg:col-span-2">
              <motion.div 
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                className="card"
              >
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                  <h3 className="text-2xl font-bold text-primary-900 mb-4 sm:mb-0 font-display">Your Tasks</h3>
                  <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                    <input 
                      type="text" 
                      placeholder="Search tasks..." 
                      className="input-primary w-full sm:w-auto"
                    />
                    <select className="input-primary w-full sm:w-auto">
                      <option value="all">All Tasks</option>
                      <option value="pending">Pending</option>
                      <option value="in-progress">In Progress</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>
                </div>

                {/* Task List */}
                <div className="space-y-4">
                  {tasks.length === 0 ? (
                    <div className="text-center py-12">
                      <CheckCircleIcon className="w-16 h-16 text-primary-300 mx-auto mb-4" />
                      <h4 className="text-xl font-semibold text-primary-600 mb-2">No tasks yet</h4>
                      <p className="text-primary-500">Create your first task to get started!</p>
                    </div>
                  ) : (
                    tasks.map((task, index) => (
                      <motion.div
                        key={task._id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`card-hover priority-${task.priority}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <h4 className="text-lg font-semibold text-primary-900 mr-3">
                                {task.taskName}
                              </h4>
                              <span className={`status-badge status-${task.status}`}>
                                {task.status.replace('-', ' ')}
                              </span>
                              <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                                task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {task.priority}
                              </span>
                            </div>
                            <p className="text-primary-700 mb-3">
                              {task.taskDescription}
                            </p>
                            <div className="flex items-center text-sm text-primary-500">
                              <ClockIcon className="w-4 h-4 mr-1" />
                              Created: {new Date(task.createdAt).toLocaleDateString()}
                              {task.dueDate && (
                                <span className="ml-4">
                                  <ClockIcon className="w-4 h-4 mr-1 inline" />
                                  Due: {new Date(task.dueDate).toLocaleDateString()}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <motion.button 
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              className="text-primary-600 hover:text-primary-800 p-2 rounded-lg transition-colors"
                              title="Toggle Status"
                            >
                              <PlayIcon className="w-5 h-5" />
                            </motion.button>
                          </div>
                        </div>
                      </motion.div>
                    ))
                  )}
                </div>
              </motion.div>
            </div>

            {/* Add Task Panel */}
            <div className="lg:col-span-1">
              <motion.div 
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                className="card sticky top-24"
              >
                <h3 className="text-xl font-bold text-primary-900 mb-6 font-display">
                  Add New Task
                </h3>
                
                <form className="space-y-4">
                  <div>
                    <label htmlFor="taskName" className="block mb-2 text-sm font-medium text-primary-900">
                      Task Name
                    </label>
                    <input 
                      type="text" 
                      id="taskName" 
                      name="taskName"
                      className="input-primary w-full" 
                      placeholder="Enter task name"
                      required 
                    />
                  </div>

                  <div>
                    <label htmlFor="taskDescription" className="block mb-2 text-sm font-medium text-primary-900">
                      Description
                    </label>
                    <textarea 
                      id="taskDescription" 
                      name="taskDescription"
                      rows={3}
                      className="input-primary w-full resize-none" 
                      placeholder="Describe your task"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="priority" className="block mb-2 text-sm font-medium text-primary-900">
                      Priority
                    </label>
                    <select 
                      id="priority" 
                      name="priority"
                      className="input-primary w-full"
                    >
                      <option value="low">Low Priority</option>
                      <option value="medium" selected>Medium Priority</option>
                      <option value="high">High Priority</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="dueDate" className="block mb-2 text-sm font-medium text-primary-900">
                      Due Date (Optional)
                    </label>
                    <input 
                      type="date" 
                      id="dueDate" 
                      name="dueDate"
                      className="input-primary w-full" 
                    />
                  </div>

                  <motion.button 
                    type="submit" 
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="btn-primary w-full py-3 font-semibold"
                  >
                    Create Task
                  </motion.button>
                </form>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
