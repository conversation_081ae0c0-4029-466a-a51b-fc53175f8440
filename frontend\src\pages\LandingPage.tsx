import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  ChartBarIcon, 
  DevicePhoneMobileIcon,
  RocketLaunchIcon,
  PlayIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const LandingPage: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const floatVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const features = [
    {
      icon: CheckCircleIcon,
      title: "Smart Task Management",
      description: "Create, organize, and prioritize your tasks with our intuitive interface. Set due dates, priorities, and track progress effortlessly."
    },
    {
      icon: ChartBarIcon,
      title: "Progress Tracking",
      description: "Monitor your productivity with detailed analytics and progress reports. See how much you've accomplished and stay motivated."
    },
    {
      icon: DevicePhoneMobileIcon,
      title: "Responsive Design",
      description: "Access your tasks anywhere, anytime. Our responsive design works perfectly on desktop, tablet, and mobile devices."
    }
  ];

  return (
    <div className="bg-secondary-50 min-h-screen">
      {/* Navigation */}
      <motion.nav 
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="nav-primary"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <motion.div 
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <h1 className="text-2xl font-bold gradient-text">
                <CheckCircleIcon className="w-8 h-8 inline mr-2 text-primary-900" />
                TaskDrive
              </h1>
            </motion.div>
            
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <a href="#features" className="text-primary-900 hover:text-primary-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Features
                </a>
                <a href="#about" className="text-primary-900 hover:text-primary-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  About
                </a>
                <Link to="/login" className="btn-primary">
                  Login
                </Link>
                <Link to="/register" className="btn-secondary">
                  Sign Up
                </Link>
              </div>
            </div>
            
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="text-primary-900 hover:text-primary-700 focus:outline-none"
              >
                {mobileMenuOpen ? (
                  <XMarkIcon className="w-6 h-6" />
                ) : (
                  <Bars3Icon className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
          
          {/* Mobile menu */}
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden"
            >
              <div className="px-2 pt-2 pb-3 space-y-1 glass">
                <a href="#features" className="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">
                  Features
                </a>
                <a href="#about" className="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">
                  About
                </a>
                <Link to="/login" className="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">
                  Login
                </Link>
                <Link to="/register" className="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">
                  Sign Up
                </Link>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-900 to-primary-700">
        <div className="max-w-7xl mx-auto">
          <motion.div 
            className="text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div variants={floatVariants} animate="animate">
              <motion.h1 
                variants={itemVariants}
                className="text-4xl md:text-6xl font-bold text-secondary-50 mb-6 font-display"
              >
                Organize Your Life with
                <span className="block text-secondary-50">TaskDrive</span>
              </motion.h1>
              
              <motion.p 
                variants={itemVariants}
                className="text-xl md:text-2xl text-secondary-100 mb-8 max-w-3xl mx-auto"
              >
                The ultimate task management solution that helps you stay productive, organized, and focused on what matters most.
              </motion.p>
              
              <motion.div 
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-4 justify-center"
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link to="/register" className="btn-secondary text-lg px-8 py-4 inline-flex items-center justify-center">
                    <RocketLaunchIcon className="w-5 h-5 mr-2" />
                    Get Started Free
                  </Link>
                </motion.div>
                
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <a href="#features" className="glass-primary text-secondary-50 hover:bg-secondary-50 hover:text-primary-900 px-8 py-4 rounded-xl font-semibold transition-all inline-flex items-center justify-center">
                    <PlayIcon className="w-5 h-5 mr-2" />
                    Learn More
                  </a>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 px-4 sm:px-6 lg:px-8 bg-secondary-50">
        <div className="max-w-7xl mx-auto">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4 font-display">
              Powerful Features for Maximum Productivity
            </h2>
            <p className="text-xl text-primary-700 max-w-2xl mx-auto">
              Everything you need to manage your tasks efficiently and achieve your goals.
            </p>
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="card-hover"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="w-8 h-8 text-secondary-50" />
                  </div>
                  <h3 className="text-xl font-bold text-primary-900 mb-4 font-display">
                    {feature.title}
                  </h3>
                  <p className="text-primary-700">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
