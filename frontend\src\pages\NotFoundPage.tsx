import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ExclamationTriangleIcon, HomeIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

const NotFoundPage: React.FC = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const floatVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="bg-gradient-to-br from-primary-900 to-primary-700 min-h-screen flex items-center justify-center p-4">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="glass rounded-2xl p-8 w-full max-w-md text-center"
      >
        <motion.div variants={floatVariants} animate="animate">
          <motion.div variants={itemVariants}>
            <ExclamationTriangleIcon className="w-24 h-24 text-yellow-500 mx-auto mb-6" />
          </motion.div>
          
          <motion.h1 variants={itemVariants} className="text-6xl font-bold gradient-text mb-4">
            404
          </motion.h1>
          
          <motion.h2 variants={itemVariants} className="text-2xl font-semibold text-primary-900 mb-4 font-display">
            Page Not Found
          </motion.h2>
          
          <motion.p variants={itemVariants} className="text-primary-700 mb-8">
            Sorry, the page you are looking for doesn't exist or has been moved.
          </motion.p>
        </motion.div>
        
        <motion.div variants={itemVariants} className="space-y-4">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/" className="btn-primary w-full inline-block py-3 text-center">
              <HomeIcon className="w-5 h-5 inline mr-2" />
              Go Home
            </Link>
          </motion.div>
          
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/dashboard" className="btn-secondary w-full inline-block py-3 text-center">
              <CheckCircleIcon className="w-5 h-5 inline mr-2" />
              Dashboard
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default NotFoundPage;
