// User Types
export interface User {
  id: string;
  FirstName: string;
  LastName: string;
  email: string;
  fullName: string;
  profilePicture?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthUser {
  id: string;
  FirstName: string;
  LastName: string;
  email: string;
  fullName: string;
  lastLogin?: Date;
}

// Task Types
export interface Task {
  _id: string;
  taskName: string;
  taskDescription: string;
  user: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type TaskStatus = 'pending' | 'in-progress' | 'completed';
export type TaskPriority = 'low' | 'medium' | 'high';

export interface CreateTaskData {
  taskName: string;
  taskDescription: string;
  priority?: TaskPriority;
  dueDate?: Date;
}

export interface UpdateTaskData {
  taskName?: string;
  taskDescription?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
}

// Auth Types
export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  FirstName: string;
  LastName: string;
  email: string;
  password: string;
  terms?: boolean;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  token: string;
  user: AuthUser;
}

// Task Statistics
export interface TaskStats {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
  completionRate: number;
}

export interface TasksResponse {
  success: boolean;
  count: number;
  stats: TaskStats;
  tasks: Task[];
}

// Filter and Sort Types
export interface TaskFilters {
  status?: TaskStatus | 'all';
  priority?: TaskPriority | 'all';
  search?: string;
}

export interface TaskSort {
  sortBy: 'createdAt' | 'updatedAt' | 'taskName' | 'priority' | 'dueDate';
  order: 'asc' | 'desc';
}

// Component Props Types
export interface MotionProps {
  initial?: any;
  animate?: any;
  exit?: any;
  transition?: any;
  whileHover?: any;
  whileTap?: any;
  variants?: any;
}

// Form Types
export interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  className?: string;
}

// Modal Types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Toast Types
export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastOptions {
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
}
