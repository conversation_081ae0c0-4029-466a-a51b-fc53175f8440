// Analytics and Performance Monitoring for TaskDrive

export interface AnalyticsEvent {
  name: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  userId?: string;
  sessionId: string;
  metadata?: Record<string, any>;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
  tags?: Record<string, string>;
}

export interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  lastActivity: number;
  pageViews: number;
  events: AnalyticsEvent[];
  device: {
    userAgent: string;
    platform: string;
    language: string;
    screenResolution: string;
    timezone: string;
  };
}

class AnalyticsManager {
  private sessionId: string;
  private userId?: string;
  private session: UserSession;
  private performanceObserver?: PerformanceObserver;
  private isEnabled: boolean = true;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.session = this.initializeSession();
    this.setupPerformanceMonitoring();
    this.setupPageVisibilityTracking();
    this.setupErrorTracking();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeSession(): UserSession {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      pageViews: 1,
      events: [],
      device: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        screenResolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    };
  }

  // Event Tracking
  trackEvent(
    name: string,
    category: string,
    action: string,
    label?: string,
    value?: number,
    metadata?: Record<string, any>
  ): void {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      name,
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      metadata
    };

    this.session.events.push(event);
    this.session.lastActivity = Date.now();

    // Send to analytics service (implement based on your needs)
    this.sendEvent(event);

    console.log('📊 Analytics Event:', event);
  }

  // User Identification
  identifyUser(userId: string, traits?: Record<string, any>): void {
    this.userId = userId;
    this.session.userId = userId;

    this.trackEvent('user_identified', 'user', 'identify', userId, undefined, traits);
  }

  // Page Tracking
  trackPageView(page: string, title?: string): void {
    this.session.pageViews++;
    this.trackEvent('page_view', 'navigation', 'view', page, undefined, {
      title,
      url: window.location.href,
      referrer: document.referrer
    });
  }

  // Task-specific Analytics
  trackTaskCreated(task: any): void {
    this.trackEvent('task_created', 'task', 'create', task.priority, undefined, {
      taskId: task._id,
      priority: task.priority,
      hasDueDate: !!task.dueDate
    });
  }

  trackTaskCompleted(task: any, timeToComplete?: number): void {
    this.trackEvent('task_completed', 'task', 'complete', task.priority, timeToComplete, {
      taskId: task._id,
      priority: task.priority,
      timeToComplete
    });
  }

  trackTaskDeleted(task: any): void {
    this.trackEvent('task_deleted', 'task', 'delete', task.status, undefined, {
      taskId: task._id,
      status: task.status,
      priority: task.priority
    });
  }

  // Authentication Analytics
  trackLogin(method: string = 'email'): void {
    this.trackEvent('user_login', 'auth', 'login', method);
  }

  trackLogout(): void {
    this.trackEvent('user_logout', 'auth', 'logout');
  }

  trackRegistration(method: string = 'email'): void {
    this.trackEvent('user_registration', 'auth', 'register', method);
  }

  // Feature Usage
  trackFeatureUsage(feature: string, action: string, context?: string): void {
    this.trackEvent('feature_usage', 'feature', action, feature, undefined, {
      context
    });
  }

  // Error Tracking
  trackError(error: Error, context?: string): void {
    this.trackEvent('error', 'error', 'exception', error.name, undefined, {
      message: error.message,
      stack: error.stack,
      context,
      url: window.location.href
    });
  }

  // Performance Monitoring
  private setupPerformanceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformanceMetric(entry);
        }
      });

      try {
        this.performanceObserver.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] });
      } catch (error) {
        console.warn('Performance Observer not supported for some entry types');
      }
    }

    // Track Core Web Vitals
    this.trackCoreWebVitals();
  }

  private trackPerformanceMetric(entry: PerformanceEntry): void {
    const metric: PerformanceMetric = {
      name: entry.name,
      value: entry.duration || (entry as any).value || 0,
      timestamp: Date.now(),
      type: 'timing',
      tags: {
        entryType: entry.entryType
      }
    };

    console.log('⚡ Performance Metric:', metric);
    // Send to performance monitoring service
  }

  private trackCoreWebVitals(): void {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.trackEvent('core_web_vital', 'performance', 'fcp', undefined, entry.startTime);
        }
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.trackEvent('core_web_vital', 'performance', 'lcp', undefined, lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.trackEvent('core_web_vital', 'performance', 'cls', undefined, clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }

  // Page Visibility Tracking
  private setupPageVisibilityTracking(): void {
    let startTime = Date.now();

    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        const timeSpent = Date.now() - startTime;
        this.trackEvent('page_visibility', 'engagement', 'hidden', undefined, timeSpent);
      } else {
        startTime = Date.now();
        this.trackEvent('page_visibility', 'engagement', 'visible');
      }
    });

    // Track time on page before unload
    window.addEventListener('beforeunload', () => {
      const timeSpent = Date.now() - startTime;
      this.trackEvent('page_unload', 'engagement', 'unload', undefined, timeSpent);
    });
  }

  // Error Tracking Setup
  private setupErrorTracking(): void {
    window.addEventListener('error', (event) => {
      this.trackError(event.error, 'global_error_handler');
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(new Error(event.reason), 'unhandled_promise_rejection');
    });
  }

  // Data Export and Privacy
  exportSessionData(): string {
    return JSON.stringify(this.session, null, 2);
  }

  clearSessionData(): void {
    this.session.events = [];
    this.session.pageViews = 0;
    this.session.startTime = Date.now();
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (!enabled) {
      this.clearSessionData();
    }
  }

  // Send events to analytics service
  private async sendEvent(event: AnalyticsEvent): Promise<void> {
    // Implement your analytics service integration here
    // Examples: Google Analytics, Mixpanel, Amplitude, etc.
    
    // For now, store in localStorage for demo purposes
    try {
      const events = JSON.parse(localStorage.getItem('taskdrive_analytics') || '[]');
      events.push(event);
      
      // Keep only last 1000 events
      if (events.length > 1000) {
        events.splice(0, events.length - 1000);
      }
      
      localStorage.setItem('taskdrive_analytics', JSON.stringify(events));
    } catch (error) {
      console.warn('Failed to store analytics event:', error);
    }
  }

  // Get analytics summary
  getAnalyticsSummary(): any {
    try {
      const events = JSON.parse(localStorage.getItem('taskdrive_analytics') || '[]');
      const summary = {
        totalEvents: events.length,
        categories: {},
        topActions: {},
        sessionDuration: Date.now() - this.session.startTime,
        pageViews: this.session.pageViews
      };

      events.forEach((event: AnalyticsEvent) => {
        // Count by category
        summary.categories[event.category] = (summary.categories[event.category] || 0) + 1;
        
        // Count by action
        summary.topActions[event.action] = (summary.topActions[event.action] || 0) + 1;
      });

      return summary;
    } catch (error) {
      return { error: 'Failed to generate summary' };
    }
  }
}

// Create singleton instance
export const analytics = new AnalyticsManager();

// Utility functions
export const trackEvent = (name: string, category: string, action: string, label?: string, value?: number, metadata?: Record<string, any>) => 
  analytics.trackEvent(name, category, action, label, value, metadata);

export const trackPageView = (page: string, title?: string) => analytics.trackPageView(page, title);
export const identifyUser = (userId: string, traits?: Record<string, any>) => analytics.identifyUser(userId, traits);
export const trackError = (error: Error, context?: string) => analytics.trackError(error, context);

// Task-specific tracking
export const trackTaskCreated = (task: any) => analytics.trackTaskCreated(task);
export const trackTaskCompleted = (task: any, timeToComplete?: number) => analytics.trackTaskCompleted(task, timeToComplete);
export const trackTaskDeleted = (task: any) => analytics.trackTaskDeleted(task);

// Auth tracking
export const trackLogin = (method?: string) => analytics.trackLogin(method);
export const trackLogout = () => analytics.trackLogout();
export const trackRegistration = (method?: string) => analytics.trackRegistration(method);

// Feature tracking
export const trackFeatureUsage = (feature: string, action: string, context?: string) => 
  analytics.trackFeatureUsage(feature, action, context);
