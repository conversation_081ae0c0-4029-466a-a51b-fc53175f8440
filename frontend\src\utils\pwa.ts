// PWA Utilities for TaskDrive

export interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  requireInteraction?: boolean;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

class PWAManager {
  private deferredPrompt: PWAInstallPrompt | null = null;
  private isInstalled = false;
  private isOnline = navigator.onLine;
  private notificationPermission: NotificationPermission = 'default';

  constructor() {
    this.init();
  }

  private init() {
    // Check if app is installed
    this.checkInstallStatus();
    
    // Listen for install prompt
    this.setupInstallPrompt();
    
    // Setup online/offline detection
    this.setupNetworkDetection();
    
    // Setup notification permission
    this.checkNotificationPermission();
    
    // Register service worker
    this.registerServiceWorker();
  }

  // Service Worker Registration
  async registerServiceWorker(): Promise<boolean> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });

        console.log('🔧 Service Worker registered successfully:', registration);

        // Handle updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.showUpdateAvailable();
              }
            });
          }
        });

        // Setup background sync
        if ('sync' in window.ServiceWorkerRegistration.prototype) {
          await registration.sync.register('retry-failed-requests');
        }

        return true;
      } catch (error) {
        console.error('❌ Service Worker registration failed:', error);
        return false;
      }
    }
    return false;
  }

  // Install Prompt Management
  private setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      this.deferredPrompt = e as any;
      this.showInstallBanner();
    });

    window.addEventListener('appinstalled', () => {
      this.isInstalled = true;
      this.hideInstallBanner();
      this.showInstallSuccess();
    });
  }

  async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }

    try {
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('✅ User accepted the install prompt');
        return true;
      } else {
        console.log('❌ User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('❌ Install prompt failed:', error);
      return false;
    } finally {
      this.deferredPrompt = null;
    }
  }

  // Network Detection
  private setupNetworkDetection() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.showOnlineStatus();
      this.retryFailedRequests();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineStatus();
    });
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  // Notification Management
  private async checkNotificationPermission() {
    if ('Notification' in window) {
      this.notificationPermission = Notification.permission;
    }
  }

  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('⚠️ This browser does not support notifications');
      return false;
    }

    if (this.notificationPermission === 'granted') {
      return true;
    }

    if (this.notificationPermission === 'denied') {
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      this.notificationPermission = permission;
      return permission === 'granted';
    } catch (error) {
      console.error('❌ Notification permission request failed:', error);
      return false;
    }
  }

  async showNotification(options: NotificationOptions): Promise<boolean> {
    if (this.notificationPermission !== 'granted') {
      const granted = await this.requestNotificationPermission();
      if (!granted) return false;
    }

    try {
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        // Use service worker for persistent notifications
        const registration = await navigator.serviceWorker.ready;
        await registration.showNotification(options.title, {
          body: options.body,
          icon: options.icon || '/icons/icon-192x192.png',
          badge: options.badge || '/icons/badge-72x72.png',
          tag: options.tag || 'taskdrive-notification',
          requireInteraction: options.requireInteraction || false,
          actions: options.actions || [],
          vibrate: [100, 50, 100],
          data: {
            timestamp: Date.now(),
            url: '/'
          }
        });
      } else {
        // Fallback to regular notification
        new Notification(options.title, {
          body: options.body,
          icon: options.icon || '/icons/icon-192x192.png',
          tag: options.tag || 'taskdrive-notification'
        });
      }
      return true;
    } catch (error) {
      console.error('❌ Failed to show notification:', error);
      return false;
    }
  }

  // Install Status
  private checkInstallStatus() {
    // Check if running in standalone mode
    this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                      (window.navigator as any).standalone === true;
  }

  getInstallStatus(): boolean {
    return this.isInstalled;
  }

  // UI Feedback Methods
  private showInstallBanner() {
    // Dispatch custom event for UI to handle
    window.dispatchEvent(new CustomEvent('pwa-install-available'));
  }

  private hideInstallBanner() {
    window.dispatchEvent(new CustomEvent('pwa-install-completed'));
  }

  private showInstallSuccess() {
    window.dispatchEvent(new CustomEvent('pwa-install-success'));
  }

  private showUpdateAvailable() {
    window.dispatchEvent(new CustomEvent('pwa-update-available'));
  }

  private showOnlineStatus() {
    window.dispatchEvent(new CustomEvent('network-online'));
  }

  private showOfflineStatus() {
    window.dispatchEvent(new CustomEvent('network-offline'));
  }

  // Background Sync
  private async retryFailedRequests() {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      try {
        const registration = await navigator.serviceWorker.ready;
        if ('sync' in registration) {
          await registration.sync.register('retry-failed-requests');
        }
      } catch (error) {
        console.error('❌ Background sync failed:', error);
      }
    }
  }

  // Cache Management
  async clearCache(): Promise<boolean> {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('🗑️ All caches cleared');
        return true;
      } catch (error) {
        console.error('❌ Failed to clear cache:', error);
        return false;
      }
    }
    return false;
  }

  async getCacheSize(): Promise<number> {
    if ('caches' in window && 'storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        return estimate.usage || 0;
      } catch (error) {
        console.error('❌ Failed to get cache size:', error);
        return 0;
      }
    }
    return 0;
  }

  // App Shortcuts
  setupAppShortcuts() {
    // Handle URL parameters for shortcuts
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const view = urlParams.get('view');

    if (action === 'create') {
      window.dispatchEvent(new CustomEvent('shortcut-create-task'));
    } else if (view === 'dashboard') {
      window.dispatchEvent(new CustomEvent('shortcut-view-dashboard'));
    }
  }

  // Share API
  async shareContent(data: { title: string; text: string; url?: string }): Promise<boolean> {
    if ('share' in navigator) {
      try {
        await navigator.share(data);
        return true;
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('❌ Share failed:', error);
        }
        return false;
      }
    }
    
    // Fallback: copy to clipboard
    if ('clipboard' in navigator) {
      try {
        const shareText = `${data.title}\n${data.text}${data.url ? '\n' + data.url : ''}`;
        await navigator.clipboard.writeText(shareText);
        return true;
      } catch (error) {
        console.error('❌ Clipboard write failed:', error);
        return false;
      }
    }
    
    return false;
  }

  // Wake Lock API (keep screen on)
  private wakeLock: any = null;

  async requestWakeLock(): Promise<boolean> {
    if ('wakeLock' in navigator) {
      try {
        this.wakeLock = await (navigator as any).wakeLock.request('screen');
        console.log('🔒 Wake lock acquired');
        return true;
      } catch (error) {
        console.error('❌ Wake lock failed:', error);
        return false;
      }
    }
    return false;
  }

  async releaseWakeLock(): Promise<void> {
    if (this.wakeLock) {
      await this.wakeLock.release();
      this.wakeLock = null;
      console.log('🔓 Wake lock released');
    }
  }
}

// Create singleton instance
export const pwaManager = new PWAManager();

// Utility functions
export const isPWAInstalled = () => pwaManager.getInstallStatus();
export const isOnline = () => pwaManager.getNetworkStatus();
export const installPWA = () => pwaManager.promptInstall();
export const showNotification = (options: NotificationOptions) => pwaManager.showNotification(options);
export const shareContent = (data: { title: string; text: string; url?: string }) => pwaManager.shareContent(data);

// Event listeners for PWA events
export const addPWAEventListener = (event: string, callback: () => void) => {
  window.addEventListener(event, callback);
};

export const removePWAEventListener = (event: string, callback: () => void) => {
  window.removeEventListener(event, callback);
};

// Task Notification System
export class TaskNotificationManager {
  private scheduledNotifications: Map<string, number> = new Map();

  async scheduleTaskReminder(task: any, reminderTime: Date): Promise<boolean> {
    const now = new Date();
    const delay = reminderTime.getTime() - now.getTime();

    if (delay <= 0) {
      return false; // Can't schedule in the past
    }

    // Clear existing reminder for this task
    this.clearTaskReminder(task._id);

    // Schedule new reminder
    const timeoutId = window.setTimeout(async () => {
      await this.showTaskReminder(task);
      this.scheduledNotifications.delete(task._id);
    }, delay);

    this.scheduledNotifications.set(task._id, timeoutId);
    return true;
  }

  clearTaskReminder(taskId: string): void {
    const timeoutId = this.scheduledNotifications.get(taskId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.scheduledNotifications.delete(taskId);
    }
  }

  private async showTaskReminder(task: any): Promise<void> {
    const options: NotificationOptions = {
      title: '⏰ Task Reminder',
      body: `Don't forget: ${task.taskName}`,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: `task-reminder-${task._id}`,
      requireInteraction: true,
      actions: [
        {
          action: 'complete',
          title: 'Mark Complete',
          icon: '/icons/checkmark.png'
        },
        {
          action: 'snooze',
          title: 'Snooze 1h',
          icon: '/icons/clock.png'
        },
        {
          action: 'view',
          title: 'View Task',
          icon: '/icons/eye.png'
        }
      ]
    };

    await pwaManager.showNotification(options);
  }

  scheduleOverdueCheck(): void {
    // Check for overdue tasks every hour
    setInterval(() => {
      window.dispatchEvent(new CustomEvent('check-overdue-tasks'));
    }, 60 * 60 * 1000); // 1 hour
  }

  async showOverdueNotification(overdueTasks: any[]): Promise<void> {
    if (overdueTasks.length === 0) return;

    const options: NotificationOptions = {
      title: '🚨 Overdue Tasks',
      body: `You have ${overdueTasks.length} overdue task${overdueTasks.length > 1 ? 's' : ''}`,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'overdue-tasks',
      requireInteraction: true,
      actions: [
        {
          action: 'view-overdue',
          title: 'View Tasks',
          icon: '/icons/list.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss',
          icon: '/icons/xmark.png'
        }
      ]
    };

    await pwaManager.showNotification(options);
  }
}

export const taskNotificationManager = new TaskNotificationManager();
