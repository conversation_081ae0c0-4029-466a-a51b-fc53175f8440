// Theme Management System for TaskDrive
import React from 'react';

export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export const lightTheme: ThemeColors = {
  primary: '#667eea',
  secondary: '#764ba2',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  surface: 'rgba(255, 255, 255, 0.1)',
  text: '#ffffff',
  textSecondary: 'rgba(255, 255, 255, 0.8)',
  border: 'rgba(255, 255, 255, 0.2)',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
};

export const darkTheme: ThemeColors = {
  primary: '#4f46e5',
  secondary: '#7c3aed',
  background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)',
  surface: 'rgba(255, 255, 255, 0.05)',
  text: '#f9fafb',
  textSecondary: 'rgba(249, 250, 251, 0.7)',
  border: 'rgba(255, 255, 255, 0.1)',
  success: '#059669',
  warning: '#d97706',
  error: '#dc2626',
  info: '#2563eb'
};

export const blueTheme: ThemeColors = {
  primary: '#2563eb',
  secondary: '#1d4ed8',
  background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',
  surface: 'rgba(255, 255, 255, 0.1)',
  text: '#ffffff',
  textSecondary: 'rgba(255, 255, 255, 0.8)',
  border: 'rgba(255, 255, 255, 0.2)',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#06b6d4'
};

export const greenTheme: ThemeColors = {
  primary: '#059669',
  secondary: '#047857',
  background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
  surface: 'rgba(255, 255, 255, 0.1)',
  text: '#ffffff',
  textSecondary: 'rgba(255, 255, 255, 0.8)',
  border: 'rgba(255, 255, 255, 0.2)',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
};

export const purpleTheme: ThemeColors = {
  primary: '#7c3aed',
  secondary: '#6d28d9',
  background: 'linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%)',
  surface: 'rgba(255, 255, 255, 0.1)',
  text: '#ffffff',
  textSecondary: 'rgba(255, 255, 255, 0.8)',
  border: 'rgba(255, 255, 255, 0.2)',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
};

export const themes = {
  light: lightTheme,
  dark: darkTheme,
  blue: blueTheme,
  green: greenTheme,
  purple: purpleTheme
};

export type ThemeName = keyof typeof themes;

class ThemeManager {
  private currentTheme: ThemeName = 'light';
  private mode: ThemeMode = 'auto';
  private mediaQuery: MediaQueryList;

  constructor() {
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.init();
  }

  private init() {
    // Load saved preferences
    this.loadPreferences();
    
    // Listen for system theme changes
    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange.bind(this));
    
    // Apply initial theme
    this.applyTheme();
  }

  private loadPreferences() {
    const savedTheme = localStorage.getItem('taskdrive-theme') as ThemeName;
    const savedMode = localStorage.getItem('taskdrive-theme-mode') as ThemeMode;
    
    if (savedTheme && themes[savedTheme]) {
      this.currentTheme = savedTheme;
    }
    
    if (savedMode) {
      this.mode = savedMode;
    }
  }

  private savePreferences() {
    localStorage.setItem('taskdrive-theme', this.currentTheme);
    localStorage.setItem('taskdrive-theme-mode', this.mode);
  }

  private handleSystemThemeChange(e: MediaQueryListEvent) {
    if (this.mode === 'auto') {
      this.applyTheme();
    }
  }

  private getEffectiveTheme(): ThemeName {
    if (this.mode === 'auto') {
      return this.mediaQuery.matches ? 'dark' : 'light';
    }
    return this.currentTheme;
  }

  setTheme(theme: ThemeName) {
    this.currentTheme = theme;
    this.mode = 'light'; // Override auto mode when manually setting theme
    this.savePreferences();
    this.applyTheme();
    this.notifyThemeChange();
  }

  setMode(mode: ThemeMode) {
    this.mode = mode;
    this.savePreferences();
    this.applyTheme();
    this.notifyThemeChange();
  }

  getCurrentTheme(): ThemeName {
    return this.getEffectiveTheme();
  }

  getCurrentMode(): ThemeMode {
    return this.mode;
  }

  getThemeColors(): ThemeColors {
    return themes[this.getEffectiveTheme()];
  }

  private applyTheme() {
    const effectiveTheme = this.getEffectiveTheme();
    const colors = themes[effectiveTheme];
    
    // Apply CSS custom properties
    const root = document.documentElement;
    
    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${this.kebabCase(key)}`, value);
    });
    
    // Update meta theme-color
    this.updateMetaThemeColor(colors.primary);
    
    // Update body class for theme-specific styles
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${effectiveTheme}`);
  }

  private updateMetaThemeColor(color: string) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.setAttribute('name', 'theme-color');
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.setAttribute('content', color);
  }

  private kebabCase(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  private notifyThemeChange() {
    window.dispatchEvent(new CustomEvent('theme-changed', {
      detail: {
        theme: this.getCurrentTheme(),
        mode: this.getCurrentMode(),
        colors: this.getThemeColors()
      }
    }));
  }

  // Utility methods for components
  isDarkMode(): boolean {
    return this.getEffectiveTheme() === 'dark';
  }

  getContrastColor(backgroundColor: string): string {
    // Simple contrast calculation
    const rgb = this.hexToRgb(backgroundColor);
    if (!rgb) return this.getThemeColors().text;
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  // Animation utilities
  createThemeTransition() {
    const style = document.createElement('style');
    style.textContent = `
      * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
      }
    `;
    document.head.appendChild(style);
    
    // Remove transition after animation
    setTimeout(() => {
      document.head.removeChild(style);
    }, 300);
  }

  // Export theme for sharing
  exportTheme(): string {
    return JSON.stringify({
      theme: this.currentTheme,
      mode: this.mode,
      timestamp: Date.now()
    });
  }

  // Import theme from string
  importTheme(themeString: string): boolean {
    try {
      const themeData = JSON.parse(themeString);
      if (themeData.theme && themes[themeData.theme]) {
        this.setTheme(themeData.theme);
        if (themeData.mode) {
          this.setMode(themeData.mode);
        }
        return true;
      }
    } catch (error) {
      console.error('Failed to import theme:', error);
    }
    return false;
  }

  // Get all available themes
  getAvailableThemes(): Array<{ name: ThemeName; colors: ThemeColors }> {
    return Object.entries(themes).map(([name, colors]) => ({
      name: name as ThemeName,
      colors
    }));
  }

  // Theme preview utility
  previewTheme(theme: ThemeName, duration: number = 3000) {
    const originalTheme = this.currentTheme;
    const originalMode = this.mode;
    
    this.setTheme(theme);
    
    setTimeout(() => {
      this.currentTheme = originalTheme;
      this.mode = originalMode;
      this.applyTheme();
      this.notifyThemeChange();
    }, duration);
  }
}

// Create singleton instance
export const themeManager = new ThemeManager();

// Utility functions
export const setTheme = (theme: ThemeName) => themeManager.setTheme(theme);
export const setThemeMode = (mode: ThemeMode) => themeManager.setMode(mode);
export const getCurrentTheme = () => themeManager.getCurrentTheme();
export const getThemeColors = () => themeManager.getThemeColors();
export const isDarkMode = () => themeManager.isDarkMode();

// React hook for theme changes
export const useTheme = () => {
  const [theme, setThemeState] = React.useState(themeManager.getCurrentTheme());
  const [colors, setColors] = React.useState(themeManager.getThemeColors());

  React.useEffect(() => {
    const handleThemeChange = (event: CustomEvent) => {
      setThemeState(event.detail.theme);
      setColors(event.detail.colors);
    };

    window.addEventListener('theme-changed', handleThemeChange as EventListener);
    
    return () => {
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, []);

  return {
    theme,
    colors,
    setTheme: themeManager.setTheme.bind(themeManager),
    setMode: themeManager.setMode.bind(themeManager),
    isDark: themeManager.isDarkMode()
  };
};
