// Frontend validation utilities for enhanced security

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface FormData {
  [key: string]: any;
}

// Email validation with comprehensive regex
export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!email) {
    errors.push('Email is required');
  } else {
    // Comprehensive email regex
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    
    if (!emailRegex.test(email)) {
      errors.push('Please enter a valid email address');
    }
    
    if (email.length > 100) {
      errors.push('Email cannot exceed 100 characters');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Password validation with security requirements
export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!password) {
    errors.push('Password is required');
  } else {
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (password.length > 128) {
      errors.push('Password cannot exceed 128 characters');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }
    
    // Check for common weak passwords
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123', 
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common. Please choose a stronger password');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Name validation (First Name, Last Name)
export const validateName = (name: string, fieldName: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!name) {
    errors.push(`${fieldName} is required`);
  } else {
    if (name.length < 2) {
      errors.push(`${fieldName} must be at least 2 characters long`);
    }
    
    if (name.length > 50) {
      errors.push(`${fieldName} cannot exceed 50 characters`);
    }
    
    // Only allow letters, spaces, hyphens, and apostrophes
    if (!/^[a-zA-Z\s\-']+$/.test(name)) {
      errors.push(`${fieldName} can only contain letters, spaces, hyphens, and apostrophes`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Task name validation
export const validateTaskName = (taskName: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!taskName) {
    errors.push('Task name is required');
  } else {
    if (taskName.length < 3) {
      errors.push('Task name must be at least 3 characters long');
    }
    
    if (taskName.length > 100) {
      errors.push('Task name cannot exceed 100 characters');
    }
    
    // Check for potentially malicious content
    if (/<script|javascript:|data:|vbscript:/i.test(taskName)) {
      errors.push('Task name contains invalid content');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Task description validation
export const validateTaskDescription = (description: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!description) {
    errors.push('Task description is required');
  } else {
    if (description.length < 10) {
      errors.push('Task description must be at least 10 characters long');
    }
    
    if (description.length > 1000) {
      errors.push('Task description cannot exceed 1000 characters');
    }
    
    // Check for potentially malicious content
    if (/<script|javascript:|data:|vbscript:/i.test(description)) {
      errors.push('Task description contains invalid content');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Priority validation
export const validatePriority = (priority: string): ValidationResult => {
  const errors: string[] = [];
  const validPriorities = ['low', 'medium', 'high'];
  
  if (!validPriorities.includes(priority)) {
    errors.push('Priority must be low, medium, or high');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Due date validation
export const validateDueDate = (dueDate: string): ValidationResult => {
  const errors: string[] = [];
  
  if (dueDate) {
    const date = new Date(dueDate);
    const now = new Date();
    
    if (isNaN(date.getTime())) {
      errors.push('Please enter a valid date');
    } else if (date < now) {
      errors.push('Due date cannot be in the past');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Comprehensive form validation
export const validateRegistrationForm = (formData: FormData): ValidationResult => {
  const allErrors: string[] = [];
  
  const firstNameValidation = validateName(formData.firstName, 'First name');
  const lastNameValidation = validateName(formData.lastName, 'Last name');
  const emailValidation = validateEmail(formData.email);
  const passwordValidation = validatePassword(formData.password);
  
  allErrors.push(...firstNameValidation.errors);
  allErrors.push(...lastNameValidation.errors);
  allErrors.push(...emailValidation.errors);
  allErrors.push(...passwordValidation.errors);
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
};

export const validateLoginForm = (formData: FormData): ValidationResult => {
  const allErrors: string[] = [];
  
  const emailValidation = validateEmail(formData.email);
  allErrors.push(...emailValidation.errors);
  
  if (!formData.password) {
    allErrors.push('Password is required');
  }
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
};

export const validateTaskForm = (formData: FormData): ValidationResult => {
  const allErrors: string[] = [];
  
  const taskNameValidation = validateTaskName(formData.taskName);
  const taskDescriptionValidation = validateTaskDescription(formData.taskDescription);
  const priorityValidation = validatePriority(formData.priority);
  
  allErrors.push(...taskNameValidation.errors);
  allErrors.push(...taskDescriptionValidation.errors);
  allErrors.push(...priorityValidation.errors);
  
  if (formData.dueDate) {
    const dueDateValidation = validateDueDate(formData.dueDate);
    allErrors.push(...dueDateValidation.errors);
  }
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
};

// Sanitize input to prevent XSS
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim();
};

// Rate limiting helper for frontend
export const createRateLimiter = (maxRequests: number, windowMs: number) => {
  const requests: number[] = [];
  
  return () => {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Remove old requests
    while (requests.length > 0 && requests[0] < windowStart) {
      requests.shift();
    }
    
    if (requests.length >= maxRequests) {
      return false; // Rate limit exceeded
    }
    
    requests.push(now);
    return true; // Request allowed
  };
};
