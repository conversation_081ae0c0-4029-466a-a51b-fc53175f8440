<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskDrive - MERN Stack</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .header { text-align: center; margin-bottom: 2rem; }
        .header h1 { font-size: 3rem; margin-bottom: 1rem; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
        .stat-card { 
            background: rgba(255, 255, 255, 0.1); 
            backdrop-filter: blur(10px); 
            border-radius: 1rem; 
            padding: 1.5rem; 
            text-align: center;
        }
        .main-content { display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; }
        .task-list, .add-task { 
            background: rgba(255, 255, 255, 0.1); 
            backdrop-filter: blur(10px); 
            border-radius: 1rem; 
            padding: 1.5rem; 
        }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
        .form-group input, .form-group textarea, .form-group select { 
            width: 100%; 
            padding: 0.75rem; 
            border: none; 
            border-radius: 0.5rem; 
            background: rgba(255, 255, 255, 0.1); 
            color: white; 
            font-size: 1rem;
        }
        .form-group input::placeholder, .form-group textarea::placeholder { color: rgba(255,255,255,0.7); }
        .btn { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border: none; 
            padding: 0.75rem 1.5rem; 
            border-radius: 0.5rem; 
            cursor: pointer; 
            font-size: 1rem; 
            font-weight: 600;
        }
        .btn:hover { transform: scale(1.02); }
        .task-item { 
            background: rgba(255, 255, 255, 0.1); 
            border-radius: 0.75rem; 
            padding: 1rem; 
            margin-bottom: 1rem; 
            border-left: 4px solid #10b981;
        }
        .task-actions { display: flex; gap: 0.5rem; margin-top: 0.5rem; }
        .btn-small { padding: 0.5rem; border-radius: 0.5rem; border: none; cursor: pointer; }
        .btn-toggle { background: rgba(59, 130, 246, 0.2); color: #60a5fa; }
        .btn-delete { background: rgba(239, 68, 68, 0.2); color: #f87171; }
        .loading { text-align: center; padding: 2rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TaskDrive</h1>
            <p>MERN Stack Task Manager - Working Version</p>
            <p id="status">Loading...</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <h3 id="totalTasks">0</h3>
                <p>Total Tasks</p>
            </div>
            <div class="stat-card">
                <h3 id="completedTasks">0</h3>
                <p>Completed</p>
            </div>
            <div class="stat-card">
                <h3 id="pendingTasks">0</h3>
                <p>Pending</p>
            </div>
        </div>

        <div class="main-content">
            <div class="task-list">
                <h3>Your Tasks</h3>
                <div id="taskContainer">
                    <div class="loading">No tasks yet. Create your first task!</div>
                </div>
            </div>

            <div class="add-task">
                <h3>➕ Add New Task</h3>
                <form id="taskForm">
                    <div class="form-group">
                        <label>Task Name</label>
                        <input type="text" id="taskName" placeholder="Enter task name" required>
                    </div>
                    <div class="form-group">
                        <label>Description</label>
                        <textarea id="taskDescription" rows="3" placeholder="Describe your task" required></textarea>
                    </div>
                    <div class="form-group">
                        <label>Priority</label>
                        <select id="priority">
                            <option value="low">Low Priority</option>
                            <option value="medium" selected>Medium Priority</option>
                            <option value="high">High Priority</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">➕ Create Task</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let token = localStorage.getItem('token');
        let tasks = [];

        // Auto login and initialize
        async function init() {
            try {
                document.getElementById('status').textContent = 'Connecting to backend...';
                
                // Try to login
                const response = await axios.post(`${API_BASE}/auth/login`, {
                    email: '<EMAIL>',
                    password: 'TestPass123'
                });
                
                if (response.data.success) {
                    token = response.data.token;
                    localStorage.setItem('token', token);
                    document.getElementById('status').textContent = `Welcome, ${response.data.user.FirstName}!`;
                    await loadTasks();
                } else {
                    document.getElementById('status').textContent = 'Login failed';
                }
            } catch (error) {
                console.error('Init failed:', error);
                document.getElementById('status').textContent = 'Backend connection failed - Demo mode';
                // Show demo tasks
                tasks = [
                    { _id: '1', taskName: 'Demo Task 1', taskDescription: 'This is a demo task', priority: 'high', status: 'pending', createdAt: new Date() },
                    { _id: '2', taskName: 'Demo Task 2', taskDescription: 'Another demo task', priority: 'medium', status: 'completed', createdAt: new Date() }
                ];
                renderTasks();
                updateStats();
            }
        }

        async function loadTasks() {
            try {
                const response = await axios.get(`${API_BASE}/tasks`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                
                if (response.data.success) {
                    tasks = response.data.tasks;
                    renderTasks();
                    updateStats();
                }
            } catch (error) {
                console.error('Load tasks failed:', error);
            }
        }

        function renderTasks() {
            const container = document.getElementById('taskContainer');
            
            if (tasks.length === 0) {
                container.innerHTML = '<div class="loading">No tasks yet. Create your first task!</div>';
                return;
            }

            container.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <h4>${task.taskName}</h4>
                    <p>${task.taskDescription}</p>
                    <p><strong>Priority:</strong> ${task.priority} | <strong>Status:</strong> ${task.status}</p>
                    <div class="task-actions">
                        <button class="btn-small btn-toggle" onclick="toggleTask('${task._id}')">🔄 Toggle</button>
                        <button class="btn-small btn-delete" onclick="deleteTask('${task._id}')">🗑️ Delete</button>
                    </div>
                </div>
            `).join('');
        }

        function updateStats() {
            document.getElementById('totalTasks').textContent = tasks.length;
            document.getElementById('completedTasks').textContent = tasks.filter(t => t.status === 'completed').length;
            document.getElementById('pendingTasks').textContent = tasks.filter(t => t.status === 'pending').length;
        }

        async function createTask(event) {
            event.preventDefault();
            
            const taskName = document.getElementById('taskName').value;
            const taskDescription = document.getElementById('taskDescription').value;
            const priority = document.getElementById('priority').value;

            try {
                const response = await axios.post(`${API_BASE}/tasks`, {
                    taskName,
                    taskDescription,
                    priority
                }, {
                    headers: { Authorization: `Bearer ${token}` }
                });

                if (response.data.success) {
                    document.getElementById('taskForm').reset();
                    await loadTasks();
                }
            } catch (error) {
                console.error('Create task failed:', error);
                // Add to demo tasks if backend fails
                const newTask = {
                    _id: Date.now().toString(),
                    taskName,
                    taskDescription,
                    priority,
                    status: 'pending',
                    createdAt: new Date()
                };
                tasks.push(newTask);
                renderTasks();
                updateStats();
                document.getElementById('taskForm').reset();
            }
        }

        async function toggleTask(taskId) {
            try {
                await axios.patch(`${API_BASE}/tasks/${taskId}/toggle`, {}, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                await loadTasks();
            } catch (error) {
                console.error('Toggle task failed:', error);
                // Demo mode toggle
                const task = tasks.find(t => t._id === taskId);
                if (task) {
                    task.status = task.status === 'completed' ? 'pending' : 'completed';
                    renderTasks();
                    updateStats();
                }
            }
        }

        async function deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task?')) return;
            
            try {
                await axios.delete(`${API_BASE}/tasks/${taskId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                await loadTasks();
            } catch (error) {
                console.error('Delete task failed:', error);
                // Demo mode delete
                tasks = tasks.filter(t => t._id !== taskId);
                renderTasks();
                updateStats();
            }
        }

        // Event listeners
        document.getElementById('taskForm').addEventListener('submit', createTask);

        // Initialize app
        init();
    </script>
</body>
</html>
