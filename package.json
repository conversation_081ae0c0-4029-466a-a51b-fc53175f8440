{"name": "drive", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "build-css": "tailwindcss -i ./src/input.css -o ./src/output.css --watch", "copy-flowbite": "cp node_modules/flowbite/dist/flowbite.min.js public/js/", "setup": "npm install && echo 'Please copy .env.example to .env and configure your environment variables'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@tailwindcss/cli": "^4.1.8", "axios": "^1.9.0", "bcrypt": "^6.0.0", "connect-flash": "^0.1.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "flowbite": "^3.1.2", "framer-motion": "^12.16.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.8"}, "devDependencies": {"nodemon": "^3.1.10"}}