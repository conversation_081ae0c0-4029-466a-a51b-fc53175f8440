/* Two-Color Theme: Deep Blue (#1e3a8a) & Pure White (#ffffff) */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');


p, h1, h2, h3, h4, h5, h6, span, button, input, label, a, select, textarea, option, optgroup, legend, fieldset, figure, figcaption, pre, code, blockquote, hr, ul, ol, li, dl, dt, dd, table, thead, tbody, tfoot, tr, th, td, form, section, article, aside, header, footer, nav, main, details, summary, dialog, address, cite, abbr, acronym, dfn{
  color: black;
}

/* Root Variables - Two Color Theme */
:root {
  /* Primary Color: Deep Blue */
  --primary: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #172554;

  /* Secondary Color: Pure White */
  --secondary: #ffffff;
  --secondary-dark: #f5f5f5;

  /* Gradients using only our two colors */
  --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  --reverse-gradient: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);

  /* Glass effects with our colors */
  --glass-primary: rgba(30, 58, 138, 0.1);
  --glass-secondary: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(30, 58, 138, 0.2);

  /* Shadows using our primary color */
  --shadow-primary: 0 10px 25px -3px rgba(30, 58, 138, 0.1);
  --shadow-primary-lg: 0 20px 40px -4px rgba(30, 58, 138, 0.15);

  /* Transitions */
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s ease-out;
}

/* Base Styles with Two-Color Theme */
body {
  font-family: 'Inter', system-ui, sans-serif;
  background: var(--secondary);
  color: var(--primary);
}

/* Animated Background - Two Color Theme */
.animated-bg {
  background: linear-gradient(-45deg, var(--primary), var(--primary-light), var(--secondary), var(--secondary-dark));
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Glass Effects - Two Color Theme */
.glass {
  background: var(--glass-secondary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-primary);
}

.glass-primary {
  background: var(--glass-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-primary);
}

/* Buttons - Two Color Theme */
.btn-primary {
  background: var(--primary);
  color: var(--secondary);
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: var(--transition);
  border: 2px solid var(--primary);
  font-family: 'Poppins', sans-serif;
}

.btn-primary:hover {
  background: var(--secondary);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary-lg);
}

.btn-secondary {
  background: var(--secondary);
  color: var(--primary);
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: var(--transition);
  border: 2px solid var(--primary);
  font-family: 'Poppins', sans-serif;
}

.btn-secondary:hover {
  background: var(--primary);
  color: var(--secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary-lg);
}

/* Gradient Text - Two Color Theme */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
}

/* Card Hover Effects */
.card-hover {
  transition: var(--transition);
  cursor: pointer;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-primary-lg);
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Loading Spinner - Two Color Theme */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-dark);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Flash Messages - Two Color Theme */
.flash-message {
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  border-left: 4px solid var(--primary);
  animation: slideInDown 0.5s ease-out;
  background: var(--glass-secondary);
  color: var(--primary);
  box-shadow: var(--shadow-primary);
}

.flash-success {
  border-left-color: var(--primary);
  background: var(--glass-secondary);
  color: var(--primary);
}

.flash-error {
  border-left-color: var(--primary-dark);
  background: var(--glass-secondary);
  color: var(--primary-dark);
}

.flash-warning {
  border-left-color: var(--primary-light);
  background: var(--glass-secondary);
  color: var(--primary);
}

/* Task Priority - Two Color Theme */
.priority-low {
  border-left: 4px solid var(--primary-light);
}

.priority-medium {
  border-left: 4px solid var(--primary);
}

.priority-high {
  border-left: 4px solid var(--primary-dark);
}

/* Status Badges - Two Color Theme */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: 'Poppins', sans-serif;
}

.status-pending {
  background: var(--glass-primary);
  color: var(--primary);
  border: 1px solid var(--primary);
}

.status-in-progress {
  background: var(--primary-light);
  color: var(--secondary);
}

.status-completed {
  background: var(--primary);
  color: var(--secondary);
}

/* Form Elements - Two Color Theme */
.form-input {
  background: var(--glass-secondary);
  border: 2px solid var(--glass-border);
  color: var(--primary);
  border-radius: 12px;
  padding: 12px 16px;
  transition: var(--transition);
  font-family: 'Inter', sans-serif;
}

.form-input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-input::placeholder {
  color: rgba(30, 58, 138, 0.5);
}

/* Custom Scrollbar - Two Color Theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-dark);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Task Cards - Two Color Theme */
.task-card {
  background: var(--glass-secondary);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 20px;
  transition: var(--transition);
  box-shadow: var(--shadow-primary);
}

.task-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-primary-lg);
}

/* Navigation - Two Color Theme */
.nav-primary {
  background: var(--glass-secondary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
}

/* Utility Classes */
.text-primary {
  color: var(--primary);
}

.text-secondary {
  color: var(--secondary);
}

.bg-primary {
  background-color: var(--primary);
}

.bg-secondary {
  background-color: var(--secondary);
}

/* Loading Overlay - Two Color Theme */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(30, 58, 138, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--secondary);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in {
  animation: slideInDown 0.5s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Responsive Design - Two Color Theme */
@media (max-width: 768px) {
  .glass {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  .btn-primary, .btn-secondary {
    padding: 14px 20px;
    font-size: 16px;
  }

  .task-card {
    margin-bottom: 1rem;
    padding: 16px;
  }

  .form-input {
    padding: 14px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .gradient-text {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .glass {
    margin: 10px;
    padding: 20px;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
    text-align: center;
  }
}
