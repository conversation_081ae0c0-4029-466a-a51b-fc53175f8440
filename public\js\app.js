// Two-Color Theme Task Management App with Framer Motion-inspired animations

// Global App Object with Motion-inspired animations
const TaskApp = {
    // Animation configurations inspired by Framer Motion
    animations: {
        fadeIn: {
            initial: { opacity: 0, transform: 'translateY(20px)' },
            animate: { opacity: 1, transform: 'translateY(0px)' },
            duration: 0.5,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        },
        slideIn: {
            initial: { opacity: 0, transform: 'translateX(-20px)' },
            animate: { opacity: 1, transform: 'translateX(0px)' },
            duration: 0.3,
            easing: 'ease-out'
        },
        scaleIn: {
            initial: { opacity: 0, transform: 'scale(0.9)' },
            animate: { opacity: 1, transform: 'scale(1)' },
            duration: 0.2,
            easing: 'ease-out'
        },
        float: {
            animate: { transform: 'translateY(-10px)' },
            duration: 2,
            easing: 'ease-in-out',
            repeat: true,
            direction: 'alternate'
        }
    },

    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupFlashMessages();
        this.setupThemeToggle();
        this.setupMotionAnimations();
        this.initializePageAnimations();
    },

    // Event Listeners
    setupEventListeners() {
        // Form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Task actions
        document.addEventListener('click', this.handleTaskActions.bind(this));
        
        // Search functionality
        const searchInput = document.getElementById('taskSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
        }

        // Filter functionality
        const filterSelect = document.getElementById('taskFilter');
        if (filterSelect) {
            filterSelect.addEventListener('change', this.handleFilter.bind(this));
        }
    },

    // Form Validation
    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        });
    },

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        
        let isValid = true;
        let message = '';

        if (required && !value) {
            isValid = false;
            message = 'This field is required';
        } else if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        } else if (field.name === 'password' && value && value.length < 8) {
            isValid = false;
            message = 'Password must be at least 8 characters long';
        }

        this.showFieldValidation(field, isValid, message);
        return isValid;
    },

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    showFieldValidation(field, isValid, message) {
        const errorElement = field.parentNode.querySelector('.field-error');
        
        if (errorElement) {
            errorElement.remove();
        }

        if (!isValid) {
            field.classList.add('border-red-500');
            field.classList.remove('border-green-500');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error text-red-500 text-sm mt-1';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        } else if (field.value.trim()) {
            field.classList.add('border-green-500');
            field.classList.remove('border-red-500');
        }
    },

    clearFieldError(field) {
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
        field.classList.remove('border-red-500');
    },

    // Form Submission Handler
    async handleFormSubmit(e) {
        const form = e.target;
        if (!form.hasAttribute('data-ajax')) return;

        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner"></span> Loading...';

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: form.method,
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                this.showNotification(result.message || 'Success!', 'success');
                if (form.hasAttribute('data-reset')) {
                    form.reset();
                }
                if (form.hasAttribute('data-redirect')) {
                    setTimeout(() => {
                        window.location.href = form.getAttribute('data-redirect');
                    }, 1500);
                }
            } else {
                this.showNotification(result.message || 'An error occurred', 'error');
            }
        } catch (error) {
            this.showNotification('Network error. Please try again.', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    },

    // Task Actions Handler
    handleTaskActions(e) {
        const target = e.target;
        
        if (target.hasAttribute('data-delete-task')) {
            e.preventDefault();
            this.deleteTask(target.getAttribute('data-delete-task'));
        } else if (target.hasAttribute('data-toggle-status')) {
            e.preventDefault();
            this.toggleTaskStatus(target.getAttribute('data-toggle-status'));
        }
    },

    async deleteTask(taskId) {
        if (!confirm('Are you sure you want to delete this task?')) return;

        try {
            const response = await fetch(`/tasks/${taskId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                document.querySelector(`[data-task-id="${taskId}"]`).remove();
                this.showNotification('Task deleted successfully', 'success');
            } else {
                this.showNotification('Failed to delete task', 'error');
            }
        } catch (error) {
            this.showNotification('Network error', 'error');
        }
    },

    async toggleTaskStatus(taskId) {
        try {
            const response = await fetch(`/tasks/${taskId}/toggle`, {
                method: 'PATCH'
            });

            if (response.ok) {
                const result = await response.json();
                this.updateTaskStatus(taskId, result.status);
                this.showNotification('Task status updated', 'success');
            } else {
                this.showNotification('Failed to update task', 'error');
            }
        } catch (error) {
            this.showNotification('Network error', 'error');
        }
    },

    updateTaskStatus(taskId, status) {
        const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
        if (taskElement) {
            const statusBadge = taskElement.querySelector('.status-badge');
            if (statusBadge) {
                statusBadge.className = `status-badge status-${status}`;
                statusBadge.textContent = status.replace('-', ' ');
            }
        }
    },

    // Search Functionality
    handleSearch(e) {
        const searchTerm = e.target.value.toLowerCase();
        const tasks = document.querySelectorAll('[data-task-item]');
        
        tasks.forEach(task => {
            const taskName = task.querySelector('.task-name').textContent.toLowerCase();
            const taskDesc = task.querySelector('.task-description').textContent.toLowerCase();
            
            if (taskName.includes(searchTerm) || taskDesc.includes(searchTerm)) {
                task.style.display = 'block';
            } else {
                task.style.display = 'none';
            }
        });
    },

    // Filter Functionality
    handleFilter(e) {
        const filterValue = e.target.value;
        const tasks = document.querySelectorAll('[data-task-item]');
        
        tasks.forEach(task => {
            if (filterValue === 'all') {
                task.style.display = 'block';
            } else {
                const taskStatus = task.getAttribute('data-task-status');
                task.style.display = taskStatus === filterValue ? 'block' : 'none';
            }
        });
    },

    // Flash Messages
    setupFlashMessages() {
        const flashMessages = document.querySelectorAll('.flash-message');
        flashMessages.forEach(message => {
            setTimeout(() => {
                message.style.opacity = '0';
                setTimeout(() => message.remove(), 300);
            }, 5000);
        });
    },

    // Notification System
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `flash-message flash-${type} fixed top-4 right-4 z-50 max-w-sm`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    },

    // Theme Toggle
    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', this.toggleTheme.bind(this));
        }
        
        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.classList.toggle('dark', savedTheme === 'dark');
    },

    toggleTheme() {
        const isDark = document.documentElement.classList.toggle('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
    },

    // Motion-inspired Animations
    setupMotionAnimations() {
        // Intersection Observer for scroll animations with motion-like behavior
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target, 'fadeIn');
                }
            });
        }, observerOptions);

        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });
    },

    // Framer Motion-inspired animate function
    animateElement(element, animationType, options = {}) {
        const animation = this.animations[animationType];
        if (!animation) return;

        // Set initial state
        Object.assign(element.style, {
            opacity: animation.initial.opacity || 1,
            transform: animation.initial.transform || 'none',
            transition: `all ${animation.duration}s ${animation.easing}`
        });

        // Trigger animation on next frame
        requestAnimationFrame(() => {
            Object.assign(element.style, {
                opacity: animation.animate.opacity || 1,
                transform: animation.animate.transform || 'none'
            });
        });

        // Handle repeat animations
        if (animation.repeat) {
            setInterval(() => {
                this.animateElement(element, animationType, options);
            }, (animation.duration * 1000) * 2);
        }
    },

    // Initialize page animations
    initializePageAnimations() {
        // Animate elements on page load
        const elementsToAnimate = [
            { selector: '.gradient-text', animation: 'fadeIn', delay: 0 },
            { selector: '.glass', animation: 'slideIn', delay: 100 },
            { selector: '.btn-primary', animation: 'scaleIn', delay: 200 },
            { selector: '.task-card', animation: 'fadeIn', delay: 300 }
        ];

        elementsToAnimate.forEach(({ selector, animation, delay }) => {
            setTimeout(() => {
                document.querySelectorAll(selector).forEach((el, index) => {
                    setTimeout(() => {
                        this.animateElement(el, animation);
                    }, index * 50); // Stagger animation
                });
            }, delay);
        });

        // Add floating animation to specific elements
        document.querySelectorAll('.float').forEach(el => {
            this.animateElement(el, 'float');
        });
    },

    // Motion-inspired hover effects
    addHoverMotion(element) {
        element.addEventListener('mouseenter', () => {
            element.style.transition = 'transform 0.2s ease-out';
            element.style.transform = 'translateY(-5px) scale(1.02)';
        });

        element.addEventListener('mouseleave', () => {
            element.style.transform = 'translateY(0px) scale(1)';
        });
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    TaskApp.init();
});

// Export for global access
window.TaskApp = TaskApp;

// Additional Utility Functions
const Utils = {
    // Format date for display
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // Format relative time
    formatRelativeTime(date) {
        const now = new Date();
        const diff = now - new Date(date);
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (days === 0) return 'Today';
        if (days === 1) return 'Yesterday';
        if (days < 7) return `${days} days ago`;
        if (days < 30) return `${Math.floor(days / 7)} weeks ago`;
        return this.formatDate(date);
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Show loading overlay
    showLoading() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.id = 'loadingOverlay';
        overlay.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(overlay);
    },

    // Hide loading overlay
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.remove();
        }
    },

    // Copy to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            TaskApp.showNotification('Copied to clipboard!', 'success');
        } catch (err) {
            console.error('Failed to copy: ', err);
            TaskApp.showNotification('Failed to copy to clipboard', 'error');
        }
    },

    // Generate random ID
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    },

    // Validate email
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Get task priority color
    getPriorityColor(priority) {
        const colors = {
            low: 'green',
            medium: 'yellow',
            high: 'red'
        };
        return colors[priority] || 'gray';
    },

    // Get status color
    getStatusColor(status) {
        const colors = {
            pending: 'gray',
            'in-progress': 'blue',
            completed: 'green'
        };
        return colors[status] || 'gray';
    }
};

// Enhanced Task Management
const TaskManager = {
    // Local storage key
    STORAGE_KEY: 'taskdrive_tasks',

    // Get tasks from localStorage (backup)
    getLocalTasks() {
        try {
            return JSON.parse(localStorage.getItem(this.STORAGE_KEY)) || [];
        } catch {
            return [];
        }
    },

    // Save tasks to localStorage (backup)
    saveLocalTasks(tasks) {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(tasks));
        } catch (err) {
            console.error('Failed to save to localStorage:', err);
        }
    },

    // Sync with server
    async syncTasks() {
        try {
            const response = await fetch('/api/tasks');
            if (response.ok) {
                const tasks = await response.json();
                this.saveLocalTasks(tasks);
                return tasks;
            }
        } catch (err) {
            console.error('Failed to sync tasks:', err);
            return this.getLocalTasks();
        }
    },

    // Calculate task statistics
    getTaskStats(tasks) {
        const total = tasks.length;
        const completed = tasks.filter(t => t.status === 'completed').length;
        const pending = tasks.filter(t => t.status === 'pending').length;
        const inProgress = tasks.filter(t => t.status === 'in-progress').length;
        const overdue = tasks.filter(t => {
            if (!t.dueDate) return false;
            return new Date(t.dueDate) < new Date() && t.status !== 'completed';
        }).length;

        return {
            total,
            completed,
            pending,
            inProgress,
            overdue,
            completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
        };
    },

    // Sort tasks
    sortTasks(tasks, sortBy = 'createdAt', order = 'desc') {
        return [...tasks].sort((a, b) => {
            let aVal = a[sortBy];
            let bVal = b[sortBy];

            if (sortBy === 'priority') {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                aVal = priorityOrder[aVal] || 0;
                bVal = priorityOrder[bVal] || 0;
            }

            if (aVal < bVal) return order === 'asc' ? -1 : 1;
            if (aVal > bVal) return order === 'asc' ? 1 : -1;
            return 0;
        });
    },

    // Filter tasks
    filterTasks(tasks, filters) {
        return tasks.filter(task => {
            if (filters.status && filters.status !== 'all' && task.status !== filters.status) {
                return false;
            }
            if (filters.priority && filters.priority !== 'all' && task.priority !== filters.priority) {
                return false;
            }
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                return task.taskName.toLowerCase().includes(searchTerm) ||
                       task.taskDescription.toLowerCase().includes(searchTerm);
            }
            return true;
        });
    }
};

// Keyboard shortcuts
const KeyboardShortcuts = {
    init() {
        document.addEventListener('keydown', this.handleKeydown.bind(this));
    },

    handleKeydown(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('taskSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Ctrl/Cmd + N for new task
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            const taskNameInput = document.getElementById('taskName');
            if (taskNameInput) {
                taskNameInput.focus();
            }
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (!modal.classList.contains('hidden')) {
                    modal.classList.add('hidden');
                }
            });
        }
    }
};

// Initialize keyboard shortcuts
document.addEventListener('DOMContentLoaded', () => {
    KeyboardShortcuts.init();
});

// Export utilities
window.Utils = Utils;
window.TaskManager = TaskManager;
