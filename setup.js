#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🚀 Setting up TaskDrive...\n');

// Check if .env exists
const envPath = path.join(__dirname, '.env');
const envExamplePath = path.join(__dirname, '.env.example');

if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
        // Copy .env.example to .env
        const envExample = fs.readFileSync(envExamplePath, 'utf8');
        
        // Generate random secrets
        const jwtSecret = crypto.randomBytes(64).toString('hex');
        const sessionSecret = crypto.randomBytes(64).toString('hex');
        
        // Replace placeholder values
        const envContent = envExample
            .replace('your-super-secret-jwt-key-here-make-it-long-and-random', jwtSecret)
            .replace('your-super-secret-session-key-here-make-it-long-and-random', sessionSecret);
        
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Created .env file with generated secrets');
    } else {
        console.log('❌ .env.example file not found');
        process.exit(1);
    }
} else {
    console.log('✅ .env file already exists');
}

// Check if MongoDB URI is configured
const envContent = fs.readFileSync(envPath, 'utf8');
if (envContent.includes('mongodb://localhost:27017/taskdrive')) {
    console.log('⚠️  Using default MongoDB URI. Make sure MongoDB is running locally.');
    console.log('   Or update MONGO_URI in .env file for cloud database.');
}

// Create public directories if they don't exist
const publicDirs = ['public/css', 'public/js', 'public/images', 'public/icons'];
publicDirs.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    }
});

console.log('\n🎉 Setup complete!');
console.log('\nNext steps:');
console.log('1. Make sure MongoDB is running');
console.log('2. Run: npm run dev');
console.log('3. Visit: http://localhost:3000');
console.log('\nFor production deployment:');
console.log('1. Update MONGO_URI in .env');
console.log('2. Set NODE_ENV=production');
console.log('3. Run: npm start');

console.log('\n📚 Documentation: Check README.md for detailed instructions');
console.log('🐛 Issues: Report bugs on GitHub');
console.log('💡 Features: Suggest improvements on GitHub');

console.log('\n🚀 Happy task managing with TaskDrive!');
