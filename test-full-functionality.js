const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testFullFunctionality() {
  console.log('🧪 Testing Full MERN Stack Functionality...\n');

  try {
    // 1. Login
    console.log('1️⃣ Testing Login...');
    const login = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'TestPass123'
    });
    
    const token = login.data.token;
    console.log('✅ Login successful');

    // 2. Create multiple tasks
    console.log('\n2️⃣ Creating test tasks...');
    
    const tasks = [
      {
        taskName: 'Complete MERN Stack Project',
        taskDescription: 'Finish the TaskDrive application with React frontend',
        priority: 'high'
      },
      {
        taskName: 'Review Code Quality',
        taskDescription: 'Check all components and API endpoints',
        priority: 'medium'
      },
      {
        taskName: 'Write Documentation',
        taskDescription: 'Create user guide and technical documentation',
        priority: 'low'
      }
    ];

    for (const task of tasks) {
      const response = await axios.post(`${API_BASE}/tasks`, task, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log(`✅ Created task: ${task.taskName}`);
    }

    // 3. Get all tasks
    console.log('\n3️⃣ Fetching all tasks...');
    const allTasks = await axios.get(`${API_BASE}/tasks`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ Retrieved ${allTasks.data.tasks.length} tasks`);
    console.log('📊 Task Statistics:', allTasks.data.stats);

    // 4. Toggle first task status
    if (allTasks.data.tasks.length > 0) {
      console.log('\n4️⃣ Testing task status toggle...');
      const firstTask = allTasks.data.tasks[0];
      
      const toggleResponse = await axios.patch(`${API_BASE}/tasks/${firstTask._id}/toggle`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`✅ Toggled task status: ${firstTask.taskName}`);
      console.log(`   Status changed to: ${toggleResponse.data.data.status}`);
    }

    // 5. Get user stats
    console.log('\n5️⃣ Getting user statistics...');
    const stats = await axios.get(`${API_BASE}/users/stats`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ User Statistics:', stats.data.stats);

    console.log('\n🎉 All functionality tests passed!');
    console.log('\n📱 Frontend URL: http://localhost:3001');
    console.log('🔗 Backend API: http://localhost:5000/api');
    console.log('\n✅ MERN Stack TaskDrive is fully functional!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testFullFunctionality();
