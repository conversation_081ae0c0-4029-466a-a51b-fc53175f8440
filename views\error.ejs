<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Server error - TaskDrive">
    <link rel="stylesheet" href="/src/output.css">
    <link rel="stylesheet" href="/css/custom.css">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <title>Server Error - TaskDrive</title>
</head>
<body class="animated-bg min-h-screen flex items-center justify-center p-4">
    
    <div class="glass rounded-2xl p-8 w-full max-w-md text-center" data-animate>
        <div class="float">
            <i class="fas fa-server text-6xl text-red-400 mb-6"></i>
            <h1 class="text-4xl font-bold gradient-text mb-4">500</h1>
            <h2 class="text-2xl font-semibold text-white mb-4">Server Error</h2>
            <p class="text-gray-300 mb-8">
                Something went wrong on our end. Please try again later or contact support if the problem persists.
            </p>
            
            <div class="space-y-4">
                <a href="/" onclick="window.location.reload()" class="btn-gradient w-full py-3">
                    <i class="fas fa-redo mr-2"></i>
                    Try Again
                </a>
                <a href="/" class="glass text-white hover:bg-white hover:text-gray-900 w-full inline-block py-3 rounded-lg font-semibold transition-all text-center">
                    <i class="fas fa-home mr-2"></i>
                    Go Home
                </a>
            </div>
            
            <% if (typeof error !== 'undefined' && error.stack) { %>
                <details class="mt-6 text-left">
                    <summary class="text-gray-400 cursor-pointer hover:text-white transition-colors">
                        Technical Details
                    </summary>
                    <pre class="mt-2 text-xs text-gray-500 bg-gray-800 p-3 rounded overflow-auto max-h-32">
<%= error.stack %>
                    </pre>
                </details>
            <% } %>
        </div>
    </div>

    <script src="/js/app.js"></script>
</body>
</html>
