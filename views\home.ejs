<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="TaskDrive Dashboard - Manage your tasks efficiently">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/src/output.css" />
    <link rel="stylesheet" href="/css/custom.css" />
    <link rel="shortcut icon" href= "<%= favicon %>" >
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <title><%= title %></title>
</head>
<body class="animated-bg min-h-screen">

    <!-- Flash Messages -->
    <% if (success_msg && success_msg.length > 0) { %>
        <div class="flash-message flash-success fixed top-4 right-4 z-50 max-w-sm">
            <i class="fas fa-check-circle mr-2"></i>
            <%= success_msg %>
        </div>
    <% } %>

    <% if (error_msg && error_msg.length > 0) { %>
        <div class="flash-message flash-error fixed top-4 right-4 z-50 max-w-sm">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <%= error_msg %>
        </div>
    <% } %>

    <!-- Navigation -->
    <nav class="glass fixed w-full z-40 top-0">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold gradient-text">
                        <i class="fas fa-tasks mr-2"></i>TaskDrive
                    </h1>
                </div>

                <div class="hidden md:flex items-center space-x-4">
                    <span class="text-white">Welcome, <%= user.FirstName %>!</span>
                    <button id="themeToggle" class="text-white hover:text-gray-300 p-2 rounded-lg transition-colors">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/user/logout" class="text-white hover:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>Logout
                    </a>
                </div>

                <div class="md:hidden">
                    <button type="button" class="text-white hover:text-gray-300 focus:outline-none" id="mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 glass">
                <span class="text-white block px-3 py-2">Welcome, <%= user.FirstName %>!</span>
                <button id="themeToggleMobile" class="text-white hover:text-gray-300 block px-3 py-2 rounded-md text-base font-medium w-full text-left">
                    <i class="fas fa-moon mr-2"></i>Toggle Theme
                </button>
                <a href="/user/logout" class="text-white hover:text-gray-300 block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-20 pb-8 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <!-- Dashboard Header -->
            <div class="mb-8" data-animate>
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                    Your Task Dashboard
                </h2>
                <p class="text-gray-300 text-lg">
                    Manage your tasks efficiently and stay productive
                </p>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="glass rounded-2xl p-6 card-hover" data-animate>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-tasks text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white"><%= tasks.length %></h3>
                            <p class="text-gray-300">Total Tasks</p>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-2xl p-6 card-hover" data-animate>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">
                                <%= tasks.filter(task => task.status === 'completed').length %>
                            </h3>
                            <p class="text-gray-300">Completed</p>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-2xl p-6 card-hover" data-animate>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">
                                <%= tasks.filter(task => task.status === 'pending').length %>
                            </h3>
                            <p class="text-gray-300">Pending</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Management Section -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Task List -->
                <div class="lg:col-span-2">
                    <div class="glass rounded-2xl p-6" data-animate>
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                            <h3 class="text-2xl font-bold text-white mb-4 sm:mb-0">Your Tasks</h3>
                            <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                                <input
                                    type="text"
                                    id="taskSearch"
                                    placeholder="Search tasks..."
                                    class="glass px-4 py-2 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 w-full sm:w-auto"
                                />
                                <select
                                    id="taskFilter"
                                    class="glass px-4 py-2 rounded-lg text-white focus:ring-2 focus:ring-blue-500 w-full sm:w-auto"
                                >
                                    <option value="all">All Tasks</option>
                                    <option value="pending">Pending</option>
                                    <option value="in-progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                        </div>

                        <!-- Task List -->
                        <div class="space-y-4" id="taskList">
                            <% if (tasks.length === 0) { %>
                                <div class="text-center py-12">
                                    <i class="fas fa-tasks text-6xl text-gray-500 mb-4"></i>
                                    <h4 class="text-xl font-semibold text-gray-400 mb-2">No tasks yet</h4>
                                    <p class="text-gray-500">Create your first task to get started!</p>
                                </div>
                            <% } else { %>
                                <% tasks.forEach(task => { %>
                                    <div class="glass rounded-lg p-4 card-hover priority-<%= task.priority %>"
                                         data-task-item
                                         data-task-id="<%= task._id %>"
                                         data-task-status="<%= task.status %>">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center mb-2">
                                                    <h4 class="task-name text-lg font-semibold text-white mr-3">
                                                        <%= task.taskName %>
                                                    </h4>
                                                    <span class="status-badge status-<%= task.status %>">
                                                        <%= task.status.replace('-', ' ') %>
                                                    </span>
                                                    <span class="ml-2 px-2 py-1 text-xs rounded-full bg-<%= task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'green' %>-500 text-white">
                                                        <%= task.priority %>
                                                    </span>
                                                </div>
                                                <p class="task-description text-gray-300 mb-3">
                                                    <%= task.taskDescription %>
                                                </p>
                                                <div class="flex items-center text-sm text-gray-400">
                                                    <i class="fas fa-calendar mr-1"></i>
                                                    Created: <%= new Date(task.createdAt).toLocaleDateString() %>
                                                    <% if (task.dueDate) { %>
                                                        <span class="ml-4">
                                                            <i class="fas fa-clock mr-1"></i>
                                                            Due: <%= new Date(task.dueDate).toLocaleDateString() %>
                                                        </span>
                                                    <% } %>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2 ml-4">
                                                <button
                                                    class="text-blue-400 hover:text-blue-300 p-2 rounded-lg transition-colors"
                                                    data-toggle-status="<%= task._id %>"
                                                    title="Toggle Status"
                                                >
                                                    <i class="fas fa-sync-alt"></i>
                                                </button>
                                                <button
                                                    class="text-yellow-400 hover:text-yellow-300 p-2 rounded-lg transition-colors"
                                                    onclick="editTask('<%= task._id %>')"
                                                    title="Edit Task"
                                                >
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button
                                                    class="text-red-400 hover:text-red-300 p-2 rounded-lg transition-colors"
                                                    data-delete-task="<%= task._id %>"
                                                    title="Delete Task"
                                                >
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <% }) %>
                            <% } %>
                        </div>
                    </div>
                </div>

                <!-- Add Task Panel -->
                <div class="lg:col-span-1">
                    <div class="glass rounded-2xl p-6 sticky top-24" data-animate>
                        <h3 class="text-xl font-bold text-white mb-6">
                            <i class="fas fa-plus mr-2"></i>Add New Task
                        </h3>

                        <form action="/tasks" method="post" class="space-y-4">
                            <div>
                                <label for="taskName" class="block mb-2 text-sm font-medium text-white">
                                    Task Name
                                </label>
                                <input
                                    type="text"
                                    id="taskName"
                                    name="taskName"
                                    class="glass w-full px-4 py-3 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                    placeholder="Enter task name"
                                    required
                                />
                            </div>

                            <div>
                                <label for="taskDescription" class="block mb-2 text-sm font-medium text-white">
                                    Description
                                </label>
                                <textarea
                                    id="taskDescription"
                                    name="taskDescription"
                                    rows="3"
                                    class="glass w-full px-4 py-3 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                                    placeholder="Describe your task"
                                    required
                                ></textarea>
                            </div>

                            <div>
                                <label for="priority" class="block mb-2 text-sm font-medium text-white">
                                    Priority
                                </label>
                                <select
                                    id="priority"
                                    name="priority"
                                    class="glass w-full px-4 py-3 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                >
                                    <option value="low">Low Priority</option>
                                    <option value="medium" selected>Medium Priority</option>
                                    <option value="high">High Priority</option>
                                </select>
                            </div>

                            <div>
                                <label for="dueDate" class="block mb-2 text-sm font-medium text-white">
                                    Due Date (Optional)
                                </label>
                                <input
                                    type="date"
                                    id="dueDate"
                                    name="dueDate"
                                    class="glass w-full px-4 py-3 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                />
                            </div>

                            <button
                                type="submit"
                                class="btn-gradient w-full py-3 font-semibold transition-all hover:scale-105"
                            >
                                <i class="fas fa-plus mr-2"></i>
                                Create Task
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="/js/app.js"></script>
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Theme toggle functionality
        function setupThemeToggle() {
            const themeToggle = document.getElementById('themeToggle');
            const themeToggleMobile = document.getElementById('themeToggleMobile');

            function toggleTheme() {
                const isDark = document.documentElement.classList.toggle('dark');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');

                // Update icons
                const icon = isDark ? 'fa-sun' : 'fa-moon';
                document.querySelectorAll('#themeToggle i, #themeToggleMobile i').forEach(el => {
                    el.className = `fas ${icon}`;
                });
            }

            themeToggle?.addEventListener('click', toggleTheme);
            themeToggleMobile?.addEventListener('click', toggleTheme);

            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            if (savedTheme === 'dark') {
                document.documentElement.classList.add('dark');
                document.querySelectorAll('#themeToggle i, #themeToggleMobile i').forEach(el => {
                    el.className = 'fas fa-sun';
                });
            }
        }

        // Edit task function
        function editTask(taskId) {
            // You can implement a modal or redirect to edit page
            console.log('Edit task:', taskId);
            // For now, let's just show an alert
            alert('Edit functionality will be implemented in the next update!');
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setupThemeToggle();

            // Set minimum date for due date input
            const dueDateInput = document.getElementById('dueDate');
            if (dueDateInput) {
                const today = new Date().toISOString().split('T')[0];
                dueDateInput.setAttribute('min', today);
            }
        });

        // Auto-hide flash messages
        setTimeout(() => {
            document.querySelectorAll('.flash-message').forEach(msg => {
                msg.style.opacity = '0';
                setTimeout(() => msg.remove(), 300);
            });
        }, 5000);
    </script>
</body>
</html>

