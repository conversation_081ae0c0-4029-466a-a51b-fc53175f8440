<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="task management, productivity, todo, organize, planning, drive">
    <meta name="description" content="TaskDrive - Your personal task management solution. Organize, prioritize, and accomplish your goals with style.">
    <meta name="author" content="Abdullah <PERSON>">
    <link rel="stylesheet" href="/src/output.css">
    <link rel="stylesheet" href="/css/custom.css">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href=`${<%= favicon %>}`  >
    <title><%= title %></title>
</head>
<body class="bg-secondary min-h-screen">
    <!-- Navigation - Two Color Theme -->
    <nav class="glass fixed w-full z-50 top-0 nav-primary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold gradient-text float">
                            <i class="fas fa-tasks mr-2 text-primary-900"></i>TaskDrive
                        </h1>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#features" class="text-primary-900 hover:text-primary-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">Features</a>
                        <a href="#about" class="text-primary-900 hover:text-primary-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="/user/login" class="btn-primary">Login</a>
                        <a href="/user/register" class="btn-secondary">Sign Up</a>
                    </div>
                </div>
                <div class="md:hidden">
                    <button type="button" class="text-primary-900 hover:text-primary-700 focus:outline-none" id="mobile-menu-button">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 glass">
                <a href="#features" class="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">Features</a>
                <a href="#about" class="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="/user/login" class="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">Login</a>
                <a href="/user/register" class="text-primary-900 hover:text-primary-700 block px-3 py-2 rounded-md text-base font-medium">Sign Up</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section - Two Color Theme -->
    <section class="pt-20 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-900 to-primary-700">
        <div class="max-w-7xl mx-auto">
            <div class="text-center">
                <div class="float" data-animate>
                    <h1 class="text-4xl md:text-6xl font-bold text-secondary mb-6 font-display">
                        Organize Your Life with
                        <span class="gradient-text block text-secondary">TaskDrive</span>
                    </h1>
                    <p class="text-xl md:text-2xl text-secondary-100 mb-8 max-w-3xl mx-auto">
                        The ultimate task management solution that helps you stay productive, organized, and focused on what matters most.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center" data-animate>
                        <a href="/user/register" class="btn-secondary text-lg px-8 py-4 inline-flex items-center justify-center">
                            <i class="fas fa-rocket mr-2"></i>
                            Get Started Free
                        </a>
                        <a href="#features" class="btn-secondary text-lg px-8 py-4 inline-flex items-center justify-center">
                            <i class="fas fa-play mr-2"></i>
                            Learn More
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section - Two Color Theme -->
    <section id="features" class="py-16 px-4 sm:px-6 lg:px-8 bg-secondary">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16" data-animate>
                <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4 font-display">
                    Powerful Features for Maximum Productivity
                </h2>
                <p class="text-xl text-primary-700 max-w-2xl mx-auto">
                    Everything you need to manage your tasks efficiently and achieve your goals.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass rounded-2xl p-8 card-hover task-card" data-animate>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tasks text-secondary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-primary mb-4 font-display">Smart Task Management</h3>
                        <p class="text-primary-700">
                            Create, organize, and prioritize your tasks with our intuitive interface. Set due dates, priorities, and track progress effortlessly.
                        </p>
                    </div>
                </div>

                <div class="glass rounded-2xl p-8 card-hover task-card" data-animate>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-chart-line text-secondary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-primary mb-4 font-display">Progress Tracking</h3>
                        <p class="text-primary-700">
                            Monitor your productivity with detailed analytics and progress reports. See how much you've accomplished and stay motivated.
                        </p>
                    </div>
                </div>

                <div class="glass rounded-2xl p-8 card-hover task-card" data-animate>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-primary-dark rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-mobile-alt text-secondary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-primary mb-4 font-display">Responsive Design</h3>
                        <p class="text-primary-700">
                            Access your tasks anywhere, anytime. Our responsive design works perfectly on desktop, tablet, and mobile devices.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div data-animate>
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                        Why Choose TaskDrive?
                    </h2>
                    <p class="text-lg text-gray-200 mb-6">
                        TaskDrive is more than just a to-do list. It's a comprehensive productivity platform designed to help you achieve your goals and maintain work-life balance.
                    </p>
                    <ul class="space-y-4">
                        <li class="flex items-center text-gray-200">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            Intuitive and user-friendly interface
                        </li>
                        <li class="flex items-center text-gray-200">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            Advanced task prioritization system
                        </li>
                        <li class="flex items-center text-gray-200">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            Real-time progress tracking
                        </li>
                        <li class="flex items-center text-gray-200">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            Secure and private data handling
                        </li>
                    </ul>
                </div>
                <div class="glass rounded-2xl p-8" data-animate>
                    <div class="text-center">
                        <div class="pulse-slow">
                            <i class="fas fa-trophy text-6xl text-yellow-400 mb-6"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4">Start Your Journey Today</h3>
                        <p class="text-gray-300 mb-6">
                            Join thousands of users who have transformed their productivity with TaskDrive.
                        </p>
                        <a href="/user/register" class="btn-gradient w-full inline-block text-center">
                            Create Free Account
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="glass py-8 px-4 sm:px-6 lg:px-8 mt-16">
        <div class="max-w-7xl mx-auto">
            <div class="text-center">
                <h3 class="text-2xl font-bold gradient-text mb-4">
                    <i class="fas fa-tasks mr-2"></i>TaskDrive
                </h3>
                <p class="text-gray-300 mb-6">
                    Your personal task management solution for a more productive life.
                </p>
                <div class="flex justify-center space-x-6 mb-6">
                    <a href="#" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fab fa-facebook text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fab fa-linkedin text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                </div>
                <p class="text-gray-400 text-sm">
                    © 2024 TaskDrive. All rights reserved. Built with ❤️ for productivity enthusiasts.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="/js/app.js"></script>
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>