<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Login to TaskDrive - Your personal task management solution">
    <link rel="stylesheet" href="/src/output.css">
    <link rel="stylesheet" href="/css/custom.css">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <title><%= title %></title>
</head>
<body class="animated-bg min-h-screen flex items-center justify-center p-4">

    <!-- Flash Messages -->
    <% if (success_msg && success_msg.length > 0) { %>
        <div class="flash-message flash-success fixed top-4 right-4 z-50 max-w-sm">
            <i class="fas fa-check-circle mr-2"></i>
            <%= success_msg %>
        </div>
    <% } %>

    <% if (error_msg && error_msg.length > 0) { %>
        <div class="flash-message flash-error fixed top-4 right-4 z-50 max-w-sm">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <%= error_msg %>
        </div>
    <% } %>

    <!-- Login Container -->
    <div class="glass rounded-2xl p-8 w-full max-w-md card-hover" data-animate>
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="float">
                <h1 class="text-3xl font-bold gradient-text mb-2">
                    <i class="fas fa-tasks mr-2"></i>TaskDrive
                </h1>
                <p class="text-gray-300">Welcome back! Please sign in to your account.</p>
            </div>
        </div>

        <!-- Login Form -->
        <form action="/user/login" method="post" data-validate>
            <div class="space-y-6">
                <!-- Email Field -->
                <div>
                    <label for="email" class="block mb-2 text-sm font-medium text-white">
                        <i class="fas fa-envelope mr-2"></i>Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value="<%= typeof formData !== 'undefined' ? formData.email || '' : '' %>"
                        class="glass w-full px-4 py-3 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        placeholder="Enter your email"
                        required
                    />
                    <% if (errors) { %>
                        <% errors.forEach(error => { %>
                            <% if (error.path === 'email') { %>
                                <div class="field-error text-red-400 text-sm mt-1">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    <%= error.msg %>
                                </div>
                            <% } %>
                        <% }) %>
                    <% } %>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block mb-2 text-sm font-medium text-white">
                        <i class="fas fa-lock mr-2"></i>Password
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="glass w-full px-4 py-3 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all pr-12"
                            placeholder="Enter your password"
                            required
                        />
                        <button
                            type="button"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white transition-colors"
                            onclick="togglePassword()"
                        >
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                    <% if (errors) { %>
                        <% errors.forEach(error => { %>
                            <% if (error.path === 'password') { %>
                                <div class="field-error text-red-400 text-sm mt-1">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    <%= error.msg %>
                                </div>
                            <% } %>
                        <% }) %>
                    <% } %>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            id="remember"
                            type="checkbox"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <label for="remember" class="ml-2 text-sm text-gray-300">
                            Remember me
                        </label>
                    </div>
                    <a href="#" class="text-sm text-blue-400 hover:text-blue-300 transition-colors">
                        Forgot password?
                    </a>
                </div>

                <!-- Submit Button -->
                <button
                    type="submit"
                    class="btn-gradient w-full py-3 text-lg font-semibold transition-all hover:scale-105"
                >
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </button>
            </div>
        </form>

        <!-- Divider -->
        <div class="my-6 flex items-center">
            <div class="flex-1 border-t border-gray-600"></div>
            <span class="px-4 text-gray-400 text-sm">or</span>
            <div class="flex-1 border-t border-gray-600"></div>
        </div>

        <!-- Sign Up Link -->
        <div class="text-center">
            <p class="text-gray-300">
                Don't have an account?
                <a href="/user/register" class="text-blue-400 hover:text-blue-300 font-semibold transition-colors">
                    Sign up here
                </a>
            </p>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-4">
            <a href="/" class="text-gray-400 hover:text-white text-sm transition-colors">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Home
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="/js/app.js"></script>
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>